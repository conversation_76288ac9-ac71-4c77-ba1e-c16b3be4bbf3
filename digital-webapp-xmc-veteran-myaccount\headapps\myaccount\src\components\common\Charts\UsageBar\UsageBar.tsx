import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { <PERSON>B<PERSON>, <PERSON><PERSON><PERSON>, VictoryAxis } from 'victory';

interface UsageBarProps {
  data: UsageBarData;
}

interface UsageBarData {
  heading?: string;
  chartType: string;
  barValue1: number;
  barValue2: number;
  barValue3: number;
  barValue4: number;
  usageWeekfrom1: string;
  usageWeekfrom2: string;
  usageWeekfrom3: string;
  usageWeekfrom4: string;
  billWeek1: number;
  billWeek2: number;
  billWeek3: number;
  billWeek4: number;
  usageWeekto1: string;
  usageWeekto2: string;
  usageWeekto3: string;
  usageWeekto4: string;
  //Styling fields for Chart from Sitecore
  paddingTop: number;
  paddingBottom: number;
  paddingLeft: number;
  paddingRight: number;
  topLeft: number;
  topRight: number;
  chartwidth: number;
  barwidth: number;
  fillcolor: string;
  domainPadding: number;
}

const UsageBar = ({ data }: UsageBarProps): JSX.Element => {
  const formatDate = (dateStr: string): string => {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    const [month, day] = dateStr.split('/').map(Number);
    if (!month || !day) return ''; // fallback

    return `${monthNames[month - 1]} ${day}`;
  };
  const {
    chartType,
    barValue1,
    barValue2,
    barValue3,
    barValue4,
    usageWeekfrom1,
    usageWeekfrom2,
    usageWeekfrom3,
    usageWeekfrom4,
    usageWeekto1,
    usageWeekto2,
    usageWeekto3,
    usageWeekto4,
    paddingBottom,
    paddingTop,
    domainPadding,
    chartwidth,
    barwidth,
  } = data;
  return (
    <>
      {/* Define Gradient */}
      <svg style={{ height: 0, width: 0 }}>
        <defs>
          <linearGradient id="chartGradient" x1="0%" y1="100%" x2="0%" y2="0%">
            <stop offset="0%" stopColor="#002554" /> {/* Orange at Bottom */}
            <stop offset="50%" stopColor="#326295" /> {/* Pink in Middle */}
            <stop offset="100%" stopColor="#78278B" /> {/* Purple at Top */}
          </linearGradient>
        </defs>
      </svg>
      {/* <Text>{heading}</Text> */}
      <VictoryChart
        padding={{
          left: 0,
          right: 0,
          top: Number(paddingTop),
          bottom: Number(paddingBottom) + 30,
        }}
        domainPadding={{ x: domainPadding * 40 }}
        //animate={{ duration: 500 }}
        width={chartwidth}
        {...{ height: 230 }}
      >
        <VictoryAxis
          tickFormat={(_t, index) => {
            const dataTyped = data as UsageBarData;
            const barValue = dataTyped[`barValue${index + 1}` as keyof UsageBarData];
            // const billValue = dataTyped[`billWeek${4 - index}` as keyof UsageBarData];
            return `\n${barValue} ${chartType === '$' ? '' : 'kWh'}`;
          }}
          style={{
            tickLabels: {
              fontSize: 16,
              padding: 40,
              whiteSpace: 'pre-line',
              lineHeight: 1.5,
              fontWeight: 400,
              fontFamily: 'OpenSans-Regular',
            },
          }}
        />

        <VictoryAxis
          style={{
            tickLabels: {
              ...{
                fontSize: 16,
                fontWeight: 'bold',
                fontFamily: 'OpenSans-Regular',
              },
            },
          }}
        />

        <VictoryBar
          cornerRadius={{ topLeft: 10, topRight: 10 }}
          style={{
            data: {
              fill: 'url(#chartGradient)',
              width: barwidth,
            },
          }}
          data={[
            { from: usageWeekfrom1, to: usageWeekto1, y: barValue1 },
            { from: usageWeekfrom2, to: usageWeekto2, y: barValue2 },
            { from: usageWeekfrom3, to: usageWeekto3, y: barValue3 },
            { from: usageWeekfrom4, to: usageWeekto4, y: barValue4 },
          ]}
          x={(d) => `${formatDate(d.from)}`}
          // x={(d) => `${d.from} \n - \n ${d.to}\n`}
          // labels={({ datum }) =>
          //   `${chartType === '$' ? '$ ' : ''}${datum.y} ${chartType === '$' ? '' : 'kWh'}`
          // }
        />
      </VictoryChart>
    </>
  );
};
export { UsageBar };
export type { UsageBarData };
export default aiLogger(UsageBar, UsageBar.name);
