"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/COA/logininfo/Logininformation.tsx":
/*!***********************************************************!*\
  !*** ./src/components/COA/logininfo/Logininformation.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CombinedComponent: function() { return /* binding */ CombinedComponent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @fortawesome/pro-light-svg-icons */ \"./node_modules/@fortawesome/pro-light-svg-icons/index.mjs\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"./node_modules/@mantine/core/esm/index.js\");\n/* harmony import */ var _mantine_form__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/form */ \"./node_modules/@mantine/form/esm/index.js\");\n/* harmony import */ var src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/hoc/ApplicationInsightsLogger */ \"./src/hoc/ApplicationInsightsLogger.tsx\");\n/* harmony import */ var components_Elements_Button_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/Elements/Button/Button */ \"./src/components/Elements/Button/Button.tsx\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var src_stores_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/stores/store */ \"./src/stores/store.ts\");\n/* harmony import */ var src_stores_coaSlice__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/stores/coaSlice */ \"./src/stores/coaSlice.ts\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var components_Elements_Tooltip_Tooltip__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! components/Elements/Tooltip/Tooltip */ \"./src/components/Elements/Tooltip/Tooltip.tsx\");\n/* harmony import */ var assets_icons_QuestionCircle__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! assets/icons/QuestionCircle */ \"./src/assets/icons/QuestionCircle.tsx\");\n/* harmony import */ var _fortawesome_pro_regular_svg_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @fortawesome/pro-regular-svg-icons */ \"./node_modules/@fortawesome/pro-regular-svg-icons/index.mjs\");\n/* harmony import */ var lib_app_insights_log_error__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lib/app-insights-log-error */ \"./src/lib/app-insights-log-error.ts\");\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n // Import useForm from Mantine\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst CombinedComponent = (props)=>{\r\n    var _props_fields_TermsConditionsURL, _props_fields, _props_fields_AcceptedCheckBoxText, _props_fields1, _props_fields_TermsConditions, _props_fields2, _props_fields_rxUsername_value, _props_fields_rxUsername, _props_fields3, _props_fields_BillingConsentToolTipText_value, _props_fields_BillingConsentToolTipText, _props_fields4, _props_fields_VerifyYourInfoText, _props_fields5, _props_fields_UsernameText, _props_fields6, _props_fields_EmailRequirementsText, _props_fields7, _props_fields_PasswordText, _props_fields8, _props_fields_PasswordCharacterLimitText, _props_fields9, _props_fields_ConfirmPasswordText, _props_fields10, _props_fields_SecurityQuestionText, _props_fields11, _props_fields12, _props_fields_SecurityQuestions, _props_fields13, _props_fields_SpecifyAnswerText, _props_fields14, _props_fields_YesCheckBoxText, _props_fields15, _props_fields_YesCheckBoxText1, _props_fields16, _props_fields_IDontCheckBoxText, _props_fields17, _props_fields18, _props_fields19, _props_fields_WhyPaperlessBillingTitleText, _props_fields20, _props_fields_WhyPaperlessBillingTitleText1, _props_fields21, _props_fields22, _props_fields23, _props_fields24, _props_fields_SubmitButtonText, _props_fields25, _props_fields_CancelButtonText, _props_fields26;\r\n    _s();\r\n    const context = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.useSitecoreContext)();\r\n    const isPageEditing = context.sitecoreContext.pageEditing;\r\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const [isAcceptedChecked, setIsAcceptedChecked] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\r\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const [showSecurityAnswer, setShowSecurityAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const [isYesCheckedForSpecialOffer, setIsYesCheckedForSpecialOffer] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\r\n    let accountInfo = undefined;\r\n    let dispatch;\r\n    if (!isPageEditing) {\r\n        accountInfo = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_6__.useAppSelector)((state)=>{\r\n            var _state_coa;\r\n            return state === null || state === void 0 ? void 0 : (_state_coa = state.coa) === null || _state_coa === void 0 ? void 0 : _state_coa.accountInfo;\r\n        });\r\n        dispatch = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_6__.useAppDispatch)();\r\n    }\r\n    const termsUrl = ((_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_TermsConditionsURL = _props_fields.TermsConditionsURL) === null || _props_fields_TermsConditionsURL === void 0 ? void 0 : _props_fields_TermsConditionsURL.value) || \"#\";\r\n    const acceptText = ((_props_fields1 = props.fields) === null || _props_fields1 === void 0 ? void 0 : (_props_fields_AcceptedCheckBoxText = _props_fields1.AcceptedCheckBoxText) === null || _props_fields_AcceptedCheckBoxText === void 0 ? void 0 : _props_fields_AcceptedCheckBoxText.value) || \"I accept the\";\r\n    const termsText = ((_props_fields2 = props.fields) === null || _props_fields2 === void 0 ? void 0 : (_props_fields_TermsConditions = _props_fields2.TermsConditions) === null || _props_fields_TermsConditions === void 0 ? void 0 : _props_fields_TermsConditions.value) || \"Terms and Conditions\";\r\n    const regexUsername = ((_props_fields3 = props.fields) === null || _props_fields3 === void 0 ? void 0 : (_props_fields_rxUsername = _props_fields3.rxUsername) === null || _props_fields_rxUsername === void 0 ? void 0 : (_props_fields_rxUsername_value = _props_fields_rxUsername.value) === null || _props_fields_rxUsername_value === void 0 ? void 0 : _props_fields_rxUsername_value.slice(1, -1)) || \"\";\r\n    const [isPaperlessChecked, setIsPaperlessChecked] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)((accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.isCommunicationMethodEmail) === true ? true : false);\r\n    const showToolTip = ((_props_fields4 = props.fields) === null || _props_fields4 === void 0 ? void 0 : (_props_fields_BillingConsentToolTipText = _props_fields4.BillingConsentToolTipText) === null || _props_fields_BillingConsentToolTipText === void 0 ? void 0 : (_props_fields_BillingConsentToolTipText_value = _props_fields_BillingConsentToolTipText.value) === null || _props_fields_BillingConsentToolTipText_value === void 0 ? void 0 : _props_fields_BillingConsentToolTipText_value.trim().length) > 0;\r\n    const storedEmail = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_6__.useAppSelector)((state)=>{\r\n        var _state_coa_accountInfo, _state_coa;\r\n        return state === null || state === void 0 ? void 0 : (_state_coa = state.coa) === null || _state_coa === void 0 ? void 0 : (_state_coa_accountInfo = _state_coa.accountInfo) === null || _state_coa_accountInfo === void 0 ? void 0 : _state_coa_accountInfo.email;\r\n    });\r\n    const [isUsernameDisabled, setIsUsernameDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    // Initialize the form with useForm\r\n    const form = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_12__.useForm)({\r\n        initialValues: {\r\n            username: \"\",\r\n            password: \"\",\r\n            confirmPassword: \"\",\r\n            securityQuestion: \"\",\r\n            securityAnswer: \"\"\r\n        },\r\n        validate: {\r\n            username: (value)=>{\r\n                var _props_fields_InvalidEmailValidationText, _props_fields;\r\n                const isValidUserName = new RegExp(regexUsername).test(value);\r\n                return isValidUserName ? null : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_InvalidEmailValidationText = _props_fields.InvalidEmailValidationText) === null || _props_fields_InvalidEmailValidationText === void 0 ? void 0 : _props_fields_InvalidEmailValidationText.value;\r\n            },\r\n            password: (value)=>{\r\n                var _props_fields_rxPassword, _props_fields, _props_fields_PasswordCharactersValidationText, _props_fields1;\r\n                const regexPattern = props === null || props === void 0 ? void 0 : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_rxPassword = _props_fields.rxPassword) === null || _props_fields_rxPassword === void 0 ? void 0 : _props_fields_rxPassword.value;\r\n                const regex = new RegExp(regexPattern).test(value);\r\n                const isValidPassword = regex || /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^]).{12,}$/.test(value);\r\n                return isValidPassword ? null : (_props_fields1 = props.fields) === null || _props_fields1 === void 0 ? void 0 : (_props_fields_PasswordCharactersValidationText = _props_fields1.PasswordCharactersValidationText) === null || _props_fields_PasswordCharactersValidationText === void 0 ? void 0 : _props_fields_PasswordCharactersValidationText.value;\r\n            },\r\n            confirmPassword: (value, values)=>{\r\n                var _props_fields_PasswordsDoNotMatch, _props_fields;\r\n                const doPasswordsMatch = value === values.password;\r\n                return doPasswordsMatch ? null : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_PasswordsDoNotMatch = _props_fields.PasswordsDoNotMatch) === null || _props_fields_PasswordsDoNotMatch === void 0 ? void 0 : _props_fields_PasswordsDoNotMatch.value;\r\n            },\r\n            securityQuestion: (value)=>{\r\n                var _props_fields_SelectQuestionText, _props_fields;\r\n                const isQuestionSelected = !!value;\r\n                return isQuestionSelected ? null : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_SelectQuestionText = _props_fields.SelectQuestionText) === null || _props_fields_SelectQuestionText === void 0 ? void 0 : _props_fields_SelectQuestionText.value;\r\n            },\r\n            securityAnswer: (value)=>{\r\n                var _props_fields_AnswerValidationText, _props_fields;\r\n                const isAnswerValid = value.length >= 3;\r\n                return isAnswerValid ? null : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_AnswerValidationText = _props_fields.AnswerValidationText) === null || _props_fields_AnswerValidationText === void 0 ? void 0 : _props_fields_AnswerValidationText.value;\r\n            }\r\n        },\r\n        validateInputOnBlur: true\r\n    });\r\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\r\n        const fetchUserByEmail = async ()=>{\r\n            if (!storedEmail) return;\r\n            try {\r\n                var _response_data;\r\n                const response = await axios__WEBPACK_IMPORTED_MODULE_13__[\"default\"].get(\"/api/forgotpassword/checkusername?username=\".concat(storedEmail));\r\n                const apiResult = response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.result;\r\n                form.setFieldValue(\"username\", !apiResult ? storedEmail : \"\");\r\n                setIsUsernameDisabled(!apiResult);\r\n            } catch (error) {\r\n                var _err_response;\r\n                const err = error;\r\n                (0,lib_app_insights_log_error__WEBPACK_IMPORTED_MODULE_10__.logErrorToAppInsights)(err, {\r\n                    componentStack: \"Login\"\r\n                });\r\n                console.error(\"Error fetching user by email:\", (_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data);\r\n            }\r\n        };\r\n        fetchUserByEmail();\r\n    }, []);\r\n    const handleSubmit = async ()=>{\r\n        if (!isAcceptedChecked) {\r\n            var _props_fields_AgreeTC, _props_fields;\r\n            setErrorMessage((_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_AgreeTC = _props_fields.AgreeTC) === null || _props_fields_AgreeTC === void 0 ? void 0 : _props_fields_AgreeTC.value);\r\n            return;\r\n        }\r\n        if (!form.validate().hasErrors) {\r\n            setErrorMessage(\"\");\r\n            setLoading(true);\r\n            var _accountInfo_organizationBpNumber;\r\n            const requestBody = {\r\n                Username: form.values.username,\r\n                Email: accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.email,\r\n                SecurityQuestion: form.values.securityQuestion,\r\n                SecurityAnswer: form.values.securityAnswer,\r\n                CustomerClassification: accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.customerClassification,\r\n                EnrollmentChannel: 1,\r\n                FirstName: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.firstName) || \"\",\r\n                LastName: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.lastName) || \"\",\r\n                LanguagePreference: \"English\",\r\n                OrganizationBPNumber: (_accountInfo_organizationBpNumber = accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.organizationBpNumber) !== null && _accountInfo_organizationBpNumber !== void 0 ? _accountInfo_organizationBpNumber : null,\r\n                Password: form.values.password,\r\n                PaperlessBilling: isPaperlessChecked,\r\n                SpecialOffers: isYesCheckedForSpecialOffer,\r\n                PersonBPNumber: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.partnerNumber) || \"\",\r\n                SendEmailInvitation: true,\r\n                isBusinessUser: accountInfo && accountInfo.customerClassification.toString() == \"3\" ? false : true,\r\n                AccountNumber: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.accountNumber) || \"\",\r\n                Portal: accountInfo && accountInfo.customerClassification.toString() == \"3\" ? 4 : 5,\r\n                isEnrollment: true\r\n            };\r\n            try {\r\n                setLoading(true);\r\n                const response = await axios__WEBPACK_IMPORTED_MODULE_13__[\"default\"].post(\"/api/createonlineaccount/create\", requestBody);\r\n                if (response.data.result) {\r\n                    var _props_fields_verifyinformationurl;\r\n                    if (!isPageEditing) {\r\n                        dispatch((0,src_stores_coaSlice__WEBPACK_IMPORTED_MODULE_7__.setAccountInfo)({\r\n                            ...accountInfo,\r\n                            email: form.values.username,\r\n                            accountNumber: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.accountNumber) || \"\",\r\n                            firstName: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.firstName) || \"\",\r\n                            lastName: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.lastName) || \"\",\r\n                            partnerNumber: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.partnerNumber) || \"\",\r\n                            isCommunicationMethodEmail: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.isCommunicationMethodEmail) === true ? true : false,\r\n                            customerClassification: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.customerClassification) || 4,\r\n                            organizationBpNumber: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.organizationBpNumber) || \"\",\r\n                            isBusiness: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.isBusiness) || false\r\n                        }));\r\n                        dispatch((0,src_stores_coaSlice__WEBPACK_IMPORTED_MODULE_7__.setSecurityQuestion)(form.values.securityQuestion));\r\n                        dispatch((0,src_stores_coaSlice__WEBPACK_IMPORTED_MODULE_7__.setSecurityAnswer)(form.values.securityAnswer));\r\n                    }\r\n                    router.push((_props_fields_verifyinformationurl = props.fields.verifyinformationurl) === null || _props_fields_verifyinformationurl === void 0 ? void 0 : _props_fields_verifyinformationurl.value.href); //'/coa/Verify-Login-Information');\r\n                } else {\r\n                    var _response_data;\r\n                    setLoading(false);\r\n                    let errMsg;\r\n                    if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.messages[0]) {\r\n                        var _response_data1;\r\n                        if (((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.messages[0]) === \"Username Duplicate Message\") {\r\n                            var _props_fields1;\r\n                            errMsg = (_props_fields1 = props.fields) === null || _props_fields1 === void 0 ? void 0 : _props_fields1.LoginEmailAlreadyExist.value;\r\n                        } else {\r\n                            var _response_data2;\r\n                            errMsg = (_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.messages[0];\r\n                        }\r\n                        setErrorMessage(errMsg);\r\n                    } else {\r\n                        errMsg = props.fields.GenericeErrorMessage.value;\r\n                        setErrorMessage(errMsg);\r\n                    }\r\n                }\r\n            } catch (error) {\r\n                var _props_fields_problemCreatingAccount;\r\n                setLoading(false);\r\n                setErrorMessage((_props_fields_problemCreatingAccount = props.fields.problemCreatingAccount) === null || _props_fields_problemCreatingAccount === void 0 ? void 0 : _props_fields_problemCreatingAccount.value);\r\n            } finally{\r\n                setLoading(false);\r\n            }\r\n        }\r\n    };\r\n    const handleCancel = async ()=>{\r\n        var _props_fields_accountinformationurl;\r\n        if (!isPageEditing) {\r\n            dispatch((0,src_stores_coaSlice__WEBPACK_IMPORTED_MODULE_7__.clearAccountInfo)());\r\n        }\r\n        router.push((_props_fields_accountinformationurl = props.fields.accountinformationurl) === null || _props_fields_accountinformationurl === void 0 ? void 0 : _props_fields_accountinformationurl.value.href); //'coa/account-information');\r\n    };\r\n    var _props_fields_SecurityQuestions_map;\r\n    //console.log('SecurityQuestions= ' + props.fields.SecurityQuestions); // Debugging line to check the data\r\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n            className: \"w-full sm:max-w-[1040px] my-10 m-auto text-start\",\r\n            children: [\r\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                    tag: \"h1\",\r\n                    field: {\r\n                        value: (_props_fields5 = props.fields) === null || _props_fields5 === void 0 ? void 0 : (_props_fields_VerifyYourInfoText = _props_fields5.VerifyYourInfoText) === null || _props_fields_VerifyYourInfoText === void 0 ? void 0 : _props_fields_VerifyYourInfoText.value\r\n                    },\r\n                    className: \"sm:text-plus3 text-base sm:mr-auto pb-6 font-primaryBold text-textUndenary\"\r\n                }, void 0, false, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                    lineNumber: 272,\r\n                    columnNumber: 9\r\n                }, undefined),\r\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                    className: \"font-primaryRegular text-textUndenary text-base\",\r\n                    tag: \"p\",\r\n                    field: {\r\n                        value: \"Enter an email and password to be associated with this account.\"\r\n                    }\r\n                }, void 0, false, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                    lineNumber: 277,\r\n                    columnNumber: 9\r\n                }, undefined),\r\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                    children: [\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"flex flex-row w-full gap-20 justify-start mt-5\",\r\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                className: \"w-full px-4 sm:px-0 sm:max-w-[500px]\",\r\n                                children: [\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex py-2 justify-between flex-col items-start gap-2\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"w-[21rem] flex text-left items-center md:w-full  \".concat(form.errors.username ? \"mb-[30px]\" : \"\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 292,\r\n                                                columnNumber: 17\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"flex flex-col\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.TextInput, {\r\n                                                        label: (_props_fields6 = props.fields) === null || _props_fields6 === void 0 ? void 0 : (_props_fields_UsernameText = _props_fields6.UsernameText) === null || _props_fields_UsernameText === void 0 ? void 0 : _props_fields_UsernameText.value,\r\n                                                        ...form.getInputProps(\"username\"),\r\n                                                        autoComplete: \"new-password\",\r\n                                                        disabled: isUsernameDisabled,\r\n                                                        styles: {\r\n                                                            input: {\r\n                                                                textAlign: \"left\",\r\n                                                                width: \"21rem\"\r\n                                                            },\r\n                                                            error: {\r\n                                                                maxWidth: \"21rem\",\r\n                                                                wordWrap: \"break-word\"\r\n                                                            }\r\n                                                        }\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 298,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                        tag: \"p\",\r\n                                                        field: {\r\n                                                            value: (_props_fields7 = props.fields) === null || _props_fields7 === void 0 ? void 0 : (_props_fields_EmailRequirementsText = _props_fields7.EmailRequirementsText) === null || _props_fields_EmailRequirementsText === void 0 ? void 0 : _props_fields_EmailRequirementsText.value\r\n                                                        },\r\n                                                        className: \"font-primaryRegular text-textUndenary mt-2 text-minus2 sm:text-minus1 text-start md:text-left sm:mr-auto\"\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 308,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 297,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                        lineNumber: 291,\r\n                                        columnNumber: 15\r\n                                    }, undefined),\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex py-2 justify-between flex-col items-start gap-2\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"w-[21rem] flex items-center md:w-full\"\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 317,\r\n                                                columnNumber: 17\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"relative\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.TextInput, {\r\n                                                        label: (_props_fields8 = props.fields) === null || _props_fields8 === void 0 ? void 0 : (_props_fields_PasswordText = _props_fields8.PasswordText) === null || _props_fields_PasswordText === void 0 ? void 0 : _props_fields_PasswordText.value,\r\n                                                        ...form.getInputProps(\"password\"),\r\n                                                        type: showPassword ? \"text\" : \"password\",\r\n                                                        autoComplete: \"new-password\",\r\n                                                        styles: {\r\n                                                            input: {\r\n                                                                textAlign: \"left\",\r\n                                                                width: \"21rem\"\r\n                                                            },\r\n                                                            error: {\r\n                                                                maxWidth: \"21rem\",\r\n                                                                wordWrap: \"break-word\"\r\n                                                            }\r\n                                                        }\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 319,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                        icon: showPassword ? _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faEyeSlash : _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faEye,\r\n                                                        className: \"absolute right-[25px] sm:right-[85px] top-[50px] cursor-pointer text-textPrimary\",\r\n                                                        onClick: ()=>setShowPassword(!showPassword)\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 329,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                        tag: \"p\",\r\n                                                        field: {\r\n                                                            value: (_props_fields9 = props.fields) === null || _props_fields9 === void 0 ? void 0 : (_props_fields_PasswordCharacterLimitText = _props_fields9.PasswordCharacterLimitText) === null || _props_fields_PasswordCharacterLimitText === void 0 ? void 0 : _props_fields_PasswordCharacterLimitText.value\r\n                                                        },\r\n                                                        className: \"mt-2 font-primaryRegular text-textUndenary text-start md:text-left sm:mr-auto text-minus2 sm:text-minus1\"\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 334,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 318,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                        lineNumber: 316,\r\n                                        columnNumber: 15\r\n                                    }, undefined),\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex py-2 justify-between flex-col items-start gap-2\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"w-[21rem] flex flex-col md:w-full \".concat(form.errors.confirmPassword ? \"mb-[30px]\" : \"\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 343,\r\n                                                columnNumber: 17\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"relative\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.TextInput, {\r\n                                                        ...form.getInputProps(\"confirmPassword\"),\r\n                                                        label: (_props_fields10 = props.fields) === null || _props_fields10 === void 0 ? void 0 : (_props_fields_ConfirmPasswordText = _props_fields10.ConfirmPasswordText) === null || _props_fields_ConfirmPasswordText === void 0 ? void 0 : _props_fields_ConfirmPasswordText.value,\r\n                                                        type: showConfirmPassword ? \"text\" : \"password\",\r\n                                                        styles: {\r\n                                                            input: {\r\n                                                                textAlign: \"left\",\r\n                                                                width: \"21rem\"\r\n                                                            },\r\n                                                            error: {\r\n                                                                maxWidth: \"21rem\",\r\n                                                                wordWrap: \"break-word\"\r\n                                                            }\r\n                                                        }\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 349,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                        icon: showConfirmPassword ? _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faEyeSlash : _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faEye,\r\n                                                        className: \"absolute right-[16px] top-[50px] cursor-pointer text-textPrimary\",\r\n                                                        onClick: ()=>setShowConfirmPassword(!showConfirmPassword)\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 358,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 348,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                        lineNumber: 342,\r\n                                        columnNumber: 15\r\n                                    }, undefined),\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex py-2 justify-between flex-col items-start gap-2\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"w-[21rem] flex items-center md:w-full flex-col\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                        tag: \"h2\",\r\n                                                        field: {\r\n                                                            value: (_props_fields11 = props.fields) === null || _props_fields11 === void 0 ? void 0 : (_props_fields_SecurityQuestionText = _props_fields11.SecurityQuestionText) === null || _props_fields_SecurityQuestionText === void 0 ? void 0 : _props_fields_SecurityQuestionText.value\r\n                                                        },\r\n                                                        className: \"font-primaryBold text-textUndenary text-plus3 mt-5 sm:text-[32px] text-left mr-auto w-full\"\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 368,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.Text, {\r\n                                                        tag: \"p\",\r\n                                                        field: (_props_fields12 = props.fields) === null || _props_fields12 === void 0 ? void 0 : _props_fields12.SelectSecurityQuestionText,\r\n                                                        className: \"font-primaryRegular text-textUndenary text-base sm:text-minus1 text-left mr-auto pt-3 pb-2\"\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 373,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 367,\r\n                                                columnNumber: 17\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\r\n                                                className: \"w-[21rem] text-[16px] text-base mt-5 font-primaryRegular text-textUndenary tracking-normal leading-[24px]\",\r\n                                                placeholder: \"Choose a security question\",\r\n                                                styles: {\r\n                                                    root: {\r\n                                                        width: \"21rem\",\r\n                                                        [\"@media (max-width: 767px)\"]: {\r\n                                                            width: \"100%\"\r\n                                                        }\r\n                                                    }\r\n                                                },\r\n                                                ...form.getInputProps(\"securityQuestion\"),\r\n                                                data: (_props_fields_SecurityQuestions_map = props === null || props === void 0 ? void 0 : (_props_fields13 = props.fields) === null || _props_fields13 === void 0 ? void 0 : (_props_fields_SecurityQuestions = _props_fields13.SecurityQuestions) === null || _props_fields_SecurityQuestions === void 0 ? void 0 : _props_fields_SecurityQuestions.map((securityquestion)=>{\r\n                                                    var _securityquestion_fields_Title, _securityquestion_fields;\r\n                                                    return securityquestion === null || securityquestion === void 0 ? void 0 : (_securityquestion_fields = securityquestion.fields) === null || _securityquestion_fields === void 0 ? void 0 : (_securityquestion_fields_Title = _securityquestion_fields.Title) === null || _securityquestion_fields_Title === void 0 ? void 0 : _securityquestion_fields_Title.value;\r\n                                                })) !== null && _props_fields_SecurityQuestions_map !== void 0 ? _props_fields_SecurityQuestions_map : [],\r\n                                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                    icon: _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faChevronDown,\r\n                                                    className: \"text-textPrimary\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 397,\r\n                                                    columnNumber: 21\r\n                                                }, void 0)\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 379,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                        lineNumber: 366,\r\n                                        columnNumber: 15\r\n                                    }, undefined),\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex py-2 justify-between flex-col items-start gap-2\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"w-[21rem] flex items-center md:w-full font-primaryBold \".concat(form.errors.securityAnswer ? \"mb-[30px]\" : \"\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 402,\r\n                                                columnNumber: 17\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"relative\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.TextInput, {\r\n                                                        label: (_props_fields14 = props.fields) === null || _props_fields14 === void 0 ? void 0 : (_props_fields_SpecifyAnswerText = _props_fields14.SpecifyAnswerText) === null || _props_fields_SpecifyAnswerText === void 0 ? void 0 : _props_fields_SpecifyAnswerText.value,\r\n                                                        ...form.getInputProps(\"securityAnswer\"),\r\n                                                        disabled: !form.values.securityQuestion,\r\n                                                        type: showSecurityAnswer ? \"text\" : \"password\",\r\n                                                        styles: {\r\n                                                            input: {\r\n                                                                textAlign: \"left\",\r\n                                                                width: \"21rem\"\r\n                                                            },\r\n                                                            error: {\r\n                                                                maxWidth: \"21rem\",\r\n                                                                wordWrap: \"break-word\",\r\n                                                                paddingLeft: \"0\"\r\n                                                            }\r\n                                                        }\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 408,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                        icon: showSecurityAnswer ? _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faEyeSlash : _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faEye,\r\n                                                        className: \"absolute right-[16px] top-[50px] cursor-pointer text-textPrimary\",\r\n                                                        onClick: ()=>setShowSecurityAnswer(!showSecurityAnswer)\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 418,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 407,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                        lineNumber: 401,\r\n                                        columnNumber: 15\r\n                                    }, undefined)\r\n                                ]\r\n                            }, void 0, true, {\r\n                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                lineNumber: 289,\r\n                                columnNumber: 13\r\n                            }, undefined)\r\n                        }, void 0, false, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                            lineNumber: 288,\r\n                            columnNumber: 11\r\n                        }, undefined),\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"w-full px-4 sm:px-0 sm:max-w-[500px] m-0\",\r\n                            children: [\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                    className: \"flex flex-col justify-center my-6 \",\r\n                                    children: [\r\n                                        ((_props_fields15 = props.fields) === null || _props_fields15 === void 0 ? void 0 : (_props_fields_YesCheckBoxText = _props_fields15.YesCheckBoxText) === null || _props_fields_YesCheckBoxText === void 0 ? void 0 : _props_fields_YesCheckBoxText.value) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            className: \"flex flex-row py-2\",\r\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Checkbox, {\r\n                                                classNames: {\r\n                                                    input: \"customChecked\"\r\n                                                },\r\n                                                checked: isYesCheckedForSpecialOffer,\r\n                                                onChange: (event)=>{\r\n                                                    setIsYesCheckedForSpecialOffer(event.currentTarget.checked);\r\n                                                },\r\n                                                styles: {\r\n                                                    body: {\r\n                                                        display: \"flex\",\r\n                                                        alignItems: \"center\"\r\n                                                    }\r\n                                                },\r\n                                                radius: \"xs\",\r\n                                                label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                    className: \"flex flex-wrap items-center text-minus1 text-textQuattuordenary text-left\",\r\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\r\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.RichText, {\r\n                                                            field: {\r\n                                                                value: (_props_fields16 = props.fields) === null || _props_fields16 === void 0 ? void 0 : (_props_fields_YesCheckBoxText1 = _props_fields16.YesCheckBoxText) === null || _props_fields_YesCheckBoxText1 === void 0 ? void 0 : _props_fields_YesCheckBoxText1.value\r\n                                                            }\r\n                                                        }, void 0, false, {\r\n                                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                            lineNumber: 447,\r\n                                                            columnNumber: 27\r\n                                                        }, void 0)\r\n                                                    }, void 0, false)\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 445,\r\n                                                    columnNumber: 23\r\n                                                }, void 0)\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 431,\r\n                                                columnNumber: 19\r\n                                            }, undefined)\r\n                                        }, void 0, false, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                            lineNumber: 430,\r\n                                            columnNumber: 17\r\n                                        }, undefined),\r\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            className: \"flex flex-row py-2\",\r\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Checkbox, {\r\n                                                classNames: {\r\n                                                    input: \"customChecked\"\r\n                                                },\r\n                                                checked: isPaperlessChecked,\r\n                                                onChange: (event)=>{\r\n                                                    setIsPaperlessChecked(event.currentTarget.checked);\r\n                                                },\r\n                                                styles: {\r\n                                                    body: {\r\n                                                        display: \"flex\",\r\n                                                        alignItems: \"center\"\r\n                                                    }\r\n                                                },\r\n                                                radius: \"xs\",\r\n                                                label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                    className: \"flex flex-wrap items-center text-minus1 text-textQuattuordenary text-left\",\r\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\r\n                                                        children: [\r\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.RichText, {\r\n                                                                field: {\r\n                                                                    value: (_props_fields17 = props.fields) === null || _props_fields17 === void 0 ? void 0 : (_props_fields_IDontCheckBoxText = _props_fields17.IDontCheckBoxText) === null || _props_fields_IDontCheckBoxText === void 0 ? void 0 : _props_fields_IDontCheckBoxText.value\r\n                                                                }\r\n                                                            }, void 0, false, {\r\n                                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                                lineNumber: 471,\r\n                                                                columnNumber: 25\r\n                                                            }, void 0),\r\n                                                            showToolTip ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                                className: \"relative left-[8px] coa-tooltip\",\r\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_Elements_Tooltip_Tooltip__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\r\n                                                                    content: (_props_fields18 = props.fields) === null || _props_fields18 === void 0 ? void 0 : _props_fields18.BillingConsentToolTipText,\r\n                                                                    className: \"billing-tooltip selected-tooltip\",\r\n                                                                    arrowclassName: \"billing-tooltip-icon\",\r\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(assets_icons_QuestionCircle__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\r\n                                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                                        lineNumber: 479,\r\n                                                                        columnNumber: 31\r\n                                                                    }, void 0)\r\n                                                                }, void 0, false, {\r\n                                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                                    lineNumber: 474,\r\n                                                                    columnNumber: 29\r\n                                                                }, void 0)\r\n                                                            }, void 0, false, {\r\n                                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                                lineNumber: 473,\r\n                                                                columnNumber: 27\r\n                                                            }, void 0) : \"\"\r\n                                                        ]\r\n                                                    }, void 0, true)\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 469,\r\n                                                    columnNumber: 21\r\n                                                }, void 0)\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 455,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        }, void 0, false, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                            lineNumber: 454,\r\n                                            columnNumber: 15\r\n                                        }, undefined),\r\n                                        ((_props_fields19 = props.fields) === null || _props_fields19 === void 0 ? void 0 : _props_fields19.WhyPaperlessBillingTitleText) && ((_props_fields20 = props.fields) === null || _props_fields20 === void 0 ? void 0 : (_props_fields_WhyPaperlessBillingTitleText = _props_fields20.WhyPaperlessBillingTitleText) === null || _props_fields_WhyPaperlessBillingTitleText === void 0 ? void 0 : _props_fields_WhyPaperlessBillingTitleText.value) !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\r\n                                            type: \"button\",\r\n                                            onClick: ()=>setShowDetails((val)=>!val),\r\n                                            className: \"flex items-center font-primaryRegular text-textUndenary mt-3 text-minus1 hover:text-textSecondary -tracking-[0.25px] cursor-pointer justify-start\",\r\n                                            children: [\r\n                                                (_props_fields21 = props.fields) === null || _props_fields21 === void 0 ? void 0 : (_props_fields_WhyPaperlessBillingTitleText1 = _props_fields21.WhyPaperlessBillingTitleText) === null || _props_fields_WhyPaperlessBillingTitleText1 === void 0 ? void 0 : _props_fields_WhyPaperlessBillingTitleText1.value,\r\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                    icon: showDetails ? _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faChevronUp : _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faChevronDown,\r\n                                                    className: \"font-primaryRegular text-minus2 pl-2\",\r\n                                                    size: \"xs\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 498,\r\n                                                    columnNumber: 21\r\n                                                }, undefined)\r\n                                            ]\r\n                                        }, void 0, true, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                            lineNumber: 492,\r\n                                            columnNumber: 19\r\n                                        }, undefined),\r\n                                        showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\r\n                                            children: [\r\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.RichText, {\r\n                                                    tag: \"p\",\r\n                                                    field: props === null || props === void 0 ? void 0 : (_props_fields22 = props.fields) === null || _props_fields22 === void 0 ? void 0 : _props_fields22.BenefitsofPaperlessText,\r\n                                                    className: \"pt-2 flex font-primaryRegular text-textUndenary text-minus1\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 507,\r\n                                                    columnNumber: 19\r\n                                                }, undefined),\r\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.RichText, {\r\n                                                    tag: \"p\",\r\n                                                    field: props === null || props === void 0 ? void 0 : (_props_fields23 = props.fields) === null || _props_fields23 === void 0 ? void 0 : _props_fields23.PaperlessBillingDropDownText,\r\n                                                    className: \"flex font-primaryRegular text-textUndenary mb-5 text-minus1\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 512,\r\n                                                    columnNumber: 19\r\n                                                }, undefined)\r\n                                            ]\r\n                                        }, void 0, true),\r\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            className: \"flex flex-row py-2\",\r\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Checkbox, {\r\n                                                checked: isAcceptedChecked,\r\n                                                onChange: (event)=>{\r\n                                                    const checked = event.currentTarget.checked;\r\n                                                    setIsAcceptedChecked(checked);\r\n                                                    if (checked) {\r\n                                                        setErrorMessage(\"\");\r\n                                                    }\r\n                                                },\r\n                                                classNames: {\r\n                                                    input: \"customChecked\"\r\n                                                },\r\n                                                styles: {\r\n                                                    body: {\r\n                                                        display: \"flex\",\r\n                                                        alignItems: \"center\"\r\n                                                    }\r\n                                                },\r\n                                                radius: \"xs\",\r\n                                                label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                    className: \"flex flex-wrap items-center text-minus1 text-textQuattuordenary text-left\",\r\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.RichText, {\r\n                                                        tag: \"p\",\r\n                                                        field: props === null || props === void 0 ? void 0 : (_props_fields24 = props.fields) === null || _props_fields24 === void 0 ? void 0 : _props_fields24.TermsConditions,\r\n                                                        className: \"pt-2 font-primaryRegular sm:flex text-textUndenary text-minus1 page-link\"\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 540,\r\n                                                        columnNumber: 23\r\n                                                    }, void 0)\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 539,\r\n                                                    columnNumber: 21\r\n                                                }, void 0)\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 520,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        }, void 0, false, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                            lineNumber: 519,\r\n                                            columnNumber: 15\r\n                                        }, undefined)\r\n                                    ]\r\n                                }, void 0, true, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                    lineNumber: 428,\r\n                                    columnNumber: 13\r\n                                }, undefined),\r\n                                errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                    className: \"bg-red-100 border border-red-400 text-textDenary px-4 py-3 rounded relative\",\r\n                                    role: \"alert\",\r\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\r\n                                        className: \"block sm:inline\",\r\n                                        children: errorMessage\r\n                                    }, void 0, false, {\r\n                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                        lineNumber: 555,\r\n                                        columnNumber: 17\r\n                                    }, undefined)\r\n                                }, void 0, false, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                    lineNumber: 551,\r\n                                    columnNumber: 15\r\n                                }, undefined),\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                    className: \"flex flex-col sm:flex-col py-2 justify-start sm:items-start items-center gap-6 mt-8\",\r\n                                    children: [\r\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_Elements_Button_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\r\n                                                className: \"sm:w-[240px] px-6 h-[56px] m-0 w-[240px]\",\r\n                                                icon: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                    icon: _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faSpinner,\r\n                                                    className: \"text-textQuinary\",\r\n                                                    size: \"xs\",\r\n                                                    spin: true\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 564,\r\n                                                    columnNumber: 23\r\n                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\r\n                                                    children: [\r\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                            icon: _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faArrowRight,\r\n                                                            className: \"hidden\"\r\n                                                        }, void 0, false, {\r\n                                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                            lineNumber: 572,\r\n                                                            columnNumber: 25\r\n                                                        }, void 0),\r\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                            icon: _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faChevronsRight,\r\n                                                            className: \"block\"\r\n                                                        }, void 0, false, {\r\n                                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                            lineNumber: 573,\r\n                                                            columnNumber: 25\r\n                                                        }, void 0)\r\n                                                    ]\r\n                                                }, void 0, true),\r\n                                                disabled: loading,\r\n                                                onClick: handleSubmit,\r\n                                                children: (_props_fields25 = props.fields) === null || _props_fields25 === void 0 ? void 0 : (_props_fields_SubmitButtonText = _props_fields25.SubmitButtonText) === null || _props_fields_SubmitButtonText === void 0 ? void 0 : _props_fields_SubmitButtonText.value\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 560,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        }, void 0, false, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                            lineNumber: 559,\r\n                                            columnNumber: 15\r\n                                        }, undefined),\r\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            className: \"flex \",\r\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_Elements_Button_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\r\n                                                className: \"w-full sm:w-[160px] px-6 m-0 border-none \",\r\n                                                variant: \"secondary\",\r\n                                                disabled: loading,\r\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                    icon: _fortawesome_pro_regular_svg_icons__WEBPACK_IMPORTED_MODULE_16__.faCircleMinus\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 588,\r\n                                                    columnNumber: 25\r\n                                                }, void 0),\r\n                                                onClick: handleCancel,\r\n                                                children: (_props_fields26 = props.fields) === null || _props_fields26 === void 0 ? void 0 : (_props_fields_CancelButtonText = _props_fields26.CancelButtonText) === null || _props_fields_CancelButtonText === void 0 ? void 0 : _props_fields_CancelButtonText.value\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 584,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        }, void 0, false, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                            lineNumber: 583,\r\n                                            columnNumber: 15\r\n                                        }, undefined)\r\n                                    ]\r\n                                }, void 0, true, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                    lineNumber: 558,\r\n                                    columnNumber: 13\r\n                                }, undefined)\r\n                            ]\r\n                        }, void 0, true, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                            lineNumber: 427,\r\n                            columnNumber: 11\r\n                        }, undefined)\r\n                    ]\r\n                }, void 0, true, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                    lineNumber: 287,\r\n                    columnNumber: 9\r\n                }, undefined)\r\n            ]\r\n        }, void 0, true, {\r\n            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n            lineNumber: 271,\r\n            columnNumber: 7\r\n        }, undefined)\r\n    }, void 0, false, {\r\n        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n        lineNumber: 270,\r\n        columnNumber: 5\r\n    }, undefined);\r\n};\r\n_s(CombinedComponent, \"gPucE/eSCUim/Q4Z9INrS8xmU5A=\", false, function() {\r\n    return [\r\n        _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.useSitecoreContext,\r\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\r\n        src_stores_store__WEBPACK_IMPORTED_MODULE_6__.useAppSelector,\r\n        _mantine_form__WEBPACK_IMPORTED_MODULE_12__.useForm\r\n    ];\r\n});\r\n_c = CombinedComponent;\r\n\r\nconst Component = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.withDatasourceCheck)()(CombinedComponent);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(Component, Component.name));\r\nvar _c, _c1;\r\n$RefreshReg$(_c, \"CombinedComponent\");\r\n$RefreshReg$(_c1, \"%default%\");\r\n\r\n\r\n;\r\n    // Wrapped in an IIFE to avoid polluting the global scope\r\n    ;\r\n    (function () {\r\n        var _a, _b;\r\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n        // to extract CSS. For backwards compatibility, we need to check we're in a\r\n        // browser context before continuing.\r\n        if (typeof self !== 'undefined' &&\r\n            // AMP / No-JS mode does not inject these helpers:\r\n            '$RefreshHelpers$' in self) {\r\n            // @ts-ignore __webpack_module__ is global\r\n            var currentExports = module.exports;\r\n            // @ts-ignore __webpack_module__ is global\r\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n            // This cannot happen in MainTemplate because the exports mismatch between\r\n            // templating and execution.\r\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n            // A module can be accepted automatically based on its exports, e.g. when\r\n            // it is a Refresh Boundary.\r\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n                // Save the previous exports signature on update so we can compare the boundary\r\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n                module.hot.dispose(function (data) {\r\n                    data.prevSignature =\r\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n                });\r\n                // Unconditionally accept an update to this module, we'll check if it's\r\n                // still a Refresh Boundary later.\r\n                // @ts-ignore importMeta is replaced in the loader\r\n                module.hot.accept();\r\n                // This field is set when the previous version of this module was a\r\n                // Refresh Boundary, letting us know we need to check for invalidation or\r\n                // enqueue an update.\r\n                if (prevSignature !== null) {\r\n                    // A boundary can become ineligible if its exports are incompatible\r\n                    // with the previous exports.\r\n                    //\r\n                    // For example, if you add/remove/change exports, we'll want to\r\n                    // re-execute the importing modules, and force those components to\r\n                    // re-render. Similarly, if you convert a class component to a\r\n                    // function, we want to invalidate the boundary.\r\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                        module.hot.invalidate();\r\n                    }\r\n                    else {\r\n                        self.$RefreshHelpers$.scheduleUpdate();\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                // Since we just executed the code for the module, it's possible that the\r\n                // new exports made it ineligible for being a boundary.\r\n                // We only care about the case when we were _previously_ a boundary,\r\n                // because we already accepted this update (accidental side effect).\r\n                var isNoLongerABoundary = prevSignature !== null;\r\n                if (isNoLongerABoundary) {\r\n                    module.hot.invalidate();\r\n                }\r\n            }\r\n        }\r\n    })();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/COA/logininfo/Logininformation.tsx\n"));

/***/ })

});