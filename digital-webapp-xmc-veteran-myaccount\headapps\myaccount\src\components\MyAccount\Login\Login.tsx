import { faArrowRight, faSpinner } from '@fortawesome/pro-light-svg-icons';
import { faChevronsRight } from '@fortawesome/pro-solid-svg-icons';
import { faEyeSlash, faEye } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { PasswordInput, TextInput, Checkbox } from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import { isTxu } from 'src/utils/util';
import BellIcon from 'assets/icons/BellIcon';
import {
  LinkField,
  TextField,
  Link,
  withDatasourceCheck,
  Field,
  RichText,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { AxiosError, AxiosResponse } from 'axios';
import axios from 'axios-1.4';
import Button from 'components/Elements/Button/Button';
import { deleteCookie, getCookie, setCookie } from 'cookies-next';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { z } from 'zod';
import { useAppDispatch } from 'src/stores/store';
import {
  clearSwapOrRenewalStatus,
  setBpNumber,
  setIsBusinessUser,
  setIsImpersonateUser,
  //setSwapOrRenewalStatus,
} from 'src/stores/authUserSlice';
import * as CONSTANTS from 'src/utils/constants';
import { isEmailValid } from 'src/utils/isEmailValid';
import { logErrorToAppInsights, logToAppInsights } from 'lib/app-insights-log-error';
import {
  ImpersonatedUserRequest,
  ImpersonatedUserResponse,
} from 'src/services/AuthenticationAPI/types';
import { useLoader } from 'src/hooks/modalhooks';
import PageBuilder from 'components/common/PageBuilder/PageBuilder';

type LoginProps = ComponentProps & {
  fields: {
    RememberMe: TextField;
    LoginTitle: TextField;
    UserName: TextField;
    Password: TextField;
    ForgotUserName: TextField;
    ForgotUserNameLink: LinkField;
    CreateAnAccount: TextField;
    CreateAccountUrl: LinkField;
    SignIn: TextField;
    LoginSubtitle: TextField;
    NewCustomerText: TextField;
    LoginContentTitle: TextField;
    UsernameErrorMessage: TextField;
    PasswordErrorMessage: TextField;
    ForgotPassword: TextField;
    ForgotPasswordLink: LinkField;
    InvalidLoginErrorMessage: Field<string>;
    SomethingWentWrong: Field<string>;
    AwaitingSDErrorMessage: Field<string>;
    ClosedAccountErrorMessage: Field<string>;
    AccountLockedErrorMessage: Field<string>;
    AccountDisabledErrorMessage: Field<string>;
    ResidentialPageUrl: LinkField;
    BussinessPageUrl: LinkField;
    PrepaidPageUrl: LinkField;
    SocialAgencyPageUrl: LinkField;
    RedirectLink: LinkField;
  };
};

export interface Tokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export type TokenData = {
  customer_classification: string;
  bp_number: string;
  org_bp_number: string;
  portal_type: string;
};

interface ErrorResponse {
  error: {
    error_description: string;
    error: string;
    IsApproved: string | null;
    IsLocked: string | null;
    message: string | null;
  };
}

const Login = (props: LoginProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;

  if (isPageEditing) return <PageBuilder componentName="Login" />;
  const branding = process.env.NEXT_PUBLIC_BRAND || 'site';
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string | string[]>('');
  const { openModal, closeAllModal } = useLoader();
  const [rememberMe, setRememberMe] = useState(false);
  const loginSchema = z.object({
    username: z
      .string()
      .pipe(
        z.string().trim().min(1, { message: props.fields.UsernameErrorMessage.value?.toString() })
      ),
    password: z.string().nonempty({ message: props.fields.PasswordErrorMessage.value?.toString() }),
  });

  type LoginFormType = z.infer<typeof loginSchema>;
  const remCookie = getCookie('rememberMe') as string;
  console.log('remCookie', remCookie);
  const form = useForm<LoginFormType>({
    initialValues: {
      username:
        remCookie !== undefined && remCookie !== ''
          ? (new URLSearchParams(remCookie).get('userName') as string)
          : '',
      password: '',
    },
    validate: zodResolver(loginSchema),
    validateInputOnBlur: true,
    validateInputOnChange: true,
  });

  useEffect(() => {
    const url = new URL(window.location.href);
    const args = new URLSearchParams(url.search);
    const guid = args.get(CONSTANTS.PARAM_AUTH_GUID);
    const action = args.get(CONSTANTS.PARAM_AUTH_ACTION);
    const username = args.get(CONSTANTS.PARAM_AUTH_USERNAME);
    const encryptedQuery = url.search;
    dispatch(setIsImpersonateUser(false));
    if (action && username) {
      let errMsg: string | string[] = '';
      form.setFieldValue('username', username);
      if (action === 'awaitingsd') errMsg = props.fields.AwaitingSDErrorMessage.value;
      else if (action === 'closed') errMsg = props.fields.ClosedAccountErrorMessage.value;
      else errMsg = props.fields.SomethingWentWrong.value;
      setErrorMsg(errMsg);
    } else if (guid || encryptedQuery.length >= 100) {
      if (guid) {
        dispatch(setIsImpersonateUser(true));
         setCookie('isImpersonatedUser', true, {
          secure: true,
          domain: process.env.NEXT_PUBLIC_COOKIES_OPTIONS_DOMAIN,
        });
      }
      ImpersonatedUserLogin(guid, encryptedQuery);
    }
  }, []);
  async function login(values: LoginFormType) {
    openModal();
    setErrorMsg('');
    localStorage.clear();
    setLoading(true);
    try {
      setLogin(values);
    } catch (error: unknown) {
      setLoading(false);
      closeAllModal();
    }
  }

  const setLogin = async (values: LoginFormType) => {
    const isValid = isEmailValid(values.username);
    let userName = values.username;
    let errMsg: string | string[] = '';
    if (isValid) {
      const response = await getUserByEmail(userName);
      userName =
        response.indicator !== undefined && response.indicator === 'Success'
          ? response.userName
          : userName;
    }

    try {
      const request = {
        username: values.username,
        password: values.password,
      };

      const authTokenResponse = await axios.post<
        LoginFormType,
        AxiosResponse<{
          access_token: string;
          refresh_token: string;
          expires_in: number;
          customer_name: string;
        }>,
        LoginFormType
      >('/api/myaccount/login', request);

      const decodedToken: JwtPayload | null = jwt.decode(
        authTokenResponse.data.access_token
      ) as TokenData;
      if (decodedToken && decodedToken.bp_number && typeof decodedToken.bp_number === 'string') {
        const tokens: Tokens = {
          access_token: authTokenResponse.data.access_token,
          refresh_token: authTokenResponse.data.refresh_token,
          expires_in: authTokenResponse.data.expires_in,
        };

        const custType = decodedToken.customer_classification?.toLowerCase();

        const portalType =
          CONSTANTS.PortalType[decodedToken.portal_type as keyof typeof CONSTANTS.PortalType];

        const isBusinessUser = custType !== 'unknown' && custType !== CONSTANTS.CUSTOMER_TYPE;

        dispatch(
          setIsBusinessUser(
            decodedToken.sap_portal_type ? decodedToken.sap_portal_type !== '4' : false
          )
        );

        userName = userName.includes('impersonate\\')
          ? userName.replace('impersonate\\', '')
          : userName;

        userName = userName.includes('cca\\') ? userName.replace('cca\\', '') : userName;

        const checkAccountResponse = await axios.get(
          `/api/usercheck/validateusername?username=${userName}&bp_number=${
            isBusinessUser ? decodedToken.org_bp_number : decodedToken.bp_number
          }&access_token=${tokens.access_token}`
        );

        dispatch(clearSwapOrRenewalStatus());

        dispatch(
          setBpNumber(isBusinessUser ? decodedToken?.org_bp_number : decodedToken?.bp_number)
        );

        const nextStep = checkAccountResponse?.data.result?.hasInActiveContracts;

        if (!nextStep) {
          setCookies(
            tokens,
            decodedToken.customer_classification,
            portalType,
            authTokenResponse.data.customer_name,
            rememberMe,
            userName
          );
          const bp = isBusinessUser ? decodedToken.org_bp_number : decodedToken.bp_number;
          logToAppInsights(
            `${branding}-login-myaccount summary-clientid`,
            `Login success for the userName ${userName}`,
            `and bpNumber ${bp}`
          );
          redirectToNextPage(portalType, checkAccountResponse.data.result?.message);
        } else {
          if (checkAccountResponse.data.result?.message === 'AwaitingSDErrorMessage') {
            errMsg = props.fields.AwaitingSDErrorMessage.value;
          } else if (checkAccountResponse.data.result?.message === 'ClosedAccountErrorMessage') {
            errMsg = props.fields.ClosedAccountErrorMessage.value;
          } else {
            errMsg = props.fields.SomethingWentWrong.value; //responseData.data.result?.message;
          }
          logToAppInsights(
            `${branding}-login-myaccount summary-clientid`,
            `Login Failed for the User ${userName}`,
            errMsg
          );
          closeAllModal();
        }
        setErrorMsg(errMsg);
      } else {
        setErrorMsg(props.fields.SomethingWentWrong.value);
        closeAllModal();
      }
    } catch (error) {
      const err = error as AxiosError<ErrorResponse>;
      logErrorToAppInsights(err, {
        componentStack: 'Login',
      });
      if (
        err.response?.data?.error &&
        err.response?.data?.error.IsLocked !== undefined &&
        err.response?.data?.error?.IsLocked === 'true'
      ) {
        errMsg = props?.fields?.AccountLockedErrorMessage.value;
      } else if (
        err.response?.data?.error &&
        err.response?.data?.error?.IsApproved !== undefined &&
        err.response?.data?.error?.IsApproved === 'false'
      ) {
        errMsg = err.response?.data?.error?.error_description;
      } else if (
        (err.response?.data?.error &&
          err.response?.data?.error?.IsApproved !== undefined &&
          (err.response?.data?.error?.IsApproved === 'true' ||
            err.response?.data?.error?.IsApproved === null)) ||
        (err.response?.data?.error?.IsLocked &&
          (err.response?.data?.error?.IsLocked === 'false' ||
            err.response?.data?.error?.IsLocked === null))
      ) {
        //invalid username
        errMsg = err.response?.data?.error?.error_description
          ? props.fields.InvalidLoginErrorMessage.value
          : err.response?.data?.error?.error_description;
      } else if (
        err.response?.data?.error &&
        err.response?.data?.error?.error_description !== undefined
      ) {
        errMsg = props.fields.InvalidLoginErrorMessage.value;
      } else {
        errMsg = props.fields.SomethingWentWrong.value;
      }
      logToAppInsights(
        `${branding}-login-myaccount summary-clientid`,
        `Login Failed for the User ${userName}`,
        errMsg
      );
      setErrorMsg(errMsg);
      closeAllModal();
    } finally {
      setLoading(false);
    }
  };

  const getUserByEmail = async (email_id: string) => {
    try {
      const response = await axios.get(`/api/userbyemail?email_id=${email_id}`);
      return response.data;
    } catch (error) {
      const err = error as AxiosError;
      logErrorToAppInsights(err, {
        componentStack: 'Login',
      });
      console.log(err.response?.data);
      return false;
    }
  };

  const ImpersonatedUserLogin = async (guid: string | null, encryptedQuery: string) => {
    if (guid || encryptedQuery?.length >= 100) {
      openModal();
      if (guid) {
        const request = {
          guid: guid,
          encryptedQuery: '',
        } as ImpersonatedUserRequest;

        const getImpersonatedUser = await axios.post<ImpersonatedUserResponse>(
          '/api/impersonatedUser',
          request
        );
        console.log('cxt', getImpersonatedUser.data.result);

        if (
          !getImpersonatedUser.data.hasErrors &&
          getImpersonatedUser?.data?.result?.indicator === 'Success'
        ) {
          setLogin({
            username: getImpersonatedUser?.data?.result?.userName,
            password: getImpersonatedUser?.data?.result?.queryEncrypted,
          });
        } else {
          setErrorMsg(props.fields.SomethingWentWrong.value);
          closeAllModal();
        }
      } else {
        const request = {
          guid: guid,
          encryptedQuery: encryptedQuery ? encryptedQuery.substring(1) : null,
        } as ImpersonatedUserRequest;

        const getImpersonatedUser = await axios.post<ImpersonatedUserResponse>(
          '/api/impersonatedUser',
          request
        );

        if (getImpersonatedUser?.data?.result?.indicator === 'Success') {
          setLogin({
            username: getImpersonatedUser?.data?.result?.userName,
            password: request.encryptedQuery,
          });
        } else {
          setErrorMsg(props.fields.SomethingWentWrong.value);
          closeAllModal();
        }
      }
    }
  };

  const setCookies = (
    tokens: Tokens,
    customer_classification: string,
    portalType: string,
    customer_name: string,
    rememberMe: boolean,
    userName: string
  ) => {
    const cookieDomain =
      process.env.NODE_ENV === 'development' ? '' : process.env.NEXT_PUBLIC_COOKIES_OPTIONS_DOMAIN;
    const combinedTokenString = JSON.stringify(tokens);
    if (rememberMe) {
      setCookie('rememberMe', 'userName=' + userName, {
        path: '/',
        sameSite: 'none',
        secure: true,
        domain: cookieDomain ? cookieDomain : '',
        // maxAge: tokens.expires_in,
      });
    } else {
      deleteCookie('rememberMe');
    }
    setCookie('.JWTAUTHTOKEN', combinedTokenString, {
      path: '/',
      sameSite: 'none',
      secure: true,
      domain: cookieDomain ? cookieDomain : '',
      maxAge: tokens.expires_in,
    });
    setCookie('AuthToken', tokens.access_token, {
      path: '/',
      sameSite: 'none',
      secure: true,
      domain: cookieDomain ? cookieDomain : '',
      maxAge: tokens.expires_in,
    });
    setCookie('customer_name', customer_name, {
      path: '/',
      sameSite: 'none',
      secure: true,
      domain: cookieDomain ? cookieDomain : '',
      // maxAge: tokens.expires_in,
    });
    setCookie('customer_classification', customer_classification, {
      path: '/',
      sameSite: 'none',
      secure: true,
      domain: cookieDomain ? cookieDomain : '',
      // maxAge: tokens.expires_in,
    });
    setCookie('PortalType', portalType, {
      path: '/',
      sameSite: 'none',
      secure: true,
      domain: cookieDomain ? cookieDomain : '',
      //  maxAge: tokens.expires_in,
    });
  };

  function redirectToNextPage(portalType: string, checkAccountMessage: string) {
    const url = new URL(window.location.href);
    const args = new URLSearchParams(url.search);
    const guid = args.get(CONSTANTS.PARAM_AUTH_GUID);
    const guidUrl = args.get(CONSTANTS.PARAM_AUTH_GUID_URL);
    if (guid && guidUrl) {
      router.push({
        pathname: guidUrl,
      });
    } else if (portalType) {
      switch (portalType) {
        case 'Residential': {
          if (checkAccountMessage === 'IsResidential') {
            router.push({
              pathname: props.fields.ResidentialPageUrl.value.href,
            });
            break;
          } else if (checkAccountMessage === 'IsPrepaid') {
            router.push({
              pathname: props.fields.PrepaidPageUrl.value.href,
            });
            break;
          } else {
            router.push({
              pathname: props.fields.RedirectLink.value.href,
            });
            break;
          }
        }
        case 'SocialAgency': {
          router.push({
            pathname: props.fields.SocialAgencyPageUrl.value.href,
          });
          break;
        }
        case 'CSPMMF':
        case 'SMBLCI': {
          router.push({
            pathname: props.fields.BussinessPageUrl.value.href,
          });
          break;
        }
        default: {
          router.push({
            pathname: props.fields.RedirectLink.value.href,
          });
          break;
        }
      }
    }
  }

  return (
    <div className="w-full flex flex-col items-center my-10 sm:my-0 sm:mb-20">
      <div className="flex flex-row w-full max-w-[760px]">
        <div className="flex flex-col sm:flex-row sm:gap-36 items-center sm:items-start w-full px-4 sm:px-0 gap-8 justify-center">
          <div className="w-full sm:max-w-[350px]">
            <RichText
              field={{ value: String(props.fields.LoginTitle.value) }}
              className="p-2 font-primaryBlack text-textPrimary sm:text-plus3 text-plus2 sm:mr-auto pb-6 sm:max-w-[200px]"
            />

            <div className="flex  gap-2 items-center ">
              <div>
                <RichText
                  field={{ value: String(props.fields.LoginSubtitle.value) }}
                  className="text-textPrimary sm:text-[32px] text-minus1 sm:mr-auto pb-4 font-primaryBold leading-[38px]"
                />
                <div className="flex gap-2">
                  <div className=" mt-1 hidden">
                    <BellIcon />
                  </div>
                  <div>
                    <RichText
                      field={{
                        value: String(props.fields.LoginContentTitle.value),
                      }}
                      className="text-textQuattuordenary sm:text-minus1 sm:pb-4 font-primaryRegular"
                    />
                  </div>
                </div>
                <RichText
                  field={{
                    value: String(props.fields.NewCustomerText.value),
                  }}
                  className="text-textQuattuordenary sm:text-[18px] pb-2 hidden sm:block font-primaryRegular page-link"
                />
                {props.fields.CreateAnAccount?.value && (
                  <Link
                    field={{
                      value: {
                        href: `${props.fields.CreateAccountUrl.value.href}`,
                      },
                    }}
                    //  target="_blank"
                    className="font-primaryBold text-textSecondary sm:text-minus1 underline underline-offset-4 decoration-2 decoration-digitalBlueBonnet decoration-textPrimary hidden sm:block"
                  >
                    {props.fields.CreateAnAccount.value}
                    <FontAwesomeIcon icon={faChevronsRight} className="text-minus3 pl-2" />
                  </Link>
                )}
              </div>
            </div>
          </div>
          <div className="w-full sm:max-w-[255px]">
            <form
              className="w-full flex flex-col gap-3"
              onSubmit={form.onSubmit((values) => login(values))}
            >
              <TextInput
                className="block "
                label={props.fields.UserName.value}
                styles={() => {
                  if (!isTxu)
                    return {
                      root: {
                        width: '100%',
                        ['@media (min-width: 1024px)']: {
                          width: '100%',
                        },
                      },
                      label: {
                        fontFamily: 'OpenSans-Bold',
                        fontSize: '18px',
                        color: '#353434',
                      },
                      error: {
                        fontFamily: 'OpenSans-Regular',
                        fontSize: '12px',
                      },
                      input: {
                        borderWidth: '1px',
                        height: '52px',
                      },
                    };
                  else
                    return {
                      root: {
                        width: '100%',
                        ['@media (min-width: 1024px)']: {
                          width: '100%',
                        },
                      },
                      input: {
                        borderWidth: '1px',
                      },
                    };
                }}
                {...form.getInputProps('username')}
              />
              <Link
                field={{
                  value: {
                    href: `${props.fields.ForgotUserNameLink.value.href}`,
                  },
                }}
                // target="_blank"
                className="text-textSecondary hover:text-textPrimary text-minus1 font-primaryBold block"
              >
                {props.fields.ForgotUserName.value}
              </Link>
              <PasswordInput
                className="block"
                label={props.fields.Password.value}
                styles={() => {
                  if (!isTxu)
                    return {
                      root: {
                        width: '100%',
                        ['@media (min-width: 1024px)']: {
                          width: '100%',
                        },
                      },
                      label: {
                        fontFamily: 'OpenSans-Bold',
                        fontSize: '18px',
                        color: '#353434',
                      },
                      error: {
                        fontFamily: 'OpenSans-Regular',
                        fontSize: '12px',
                      },
                      input: {
                        borderWidth: '1px',
                        height: '52px',
                      },
                      innerInput: {
                        height: '52px',
                        fontSize: '12px',
                      },
                    };
                  else
                    return {
                      root: {
                        width: '100%',
                        ['@media (min-width: 1024px)']: {
                          width: '100%',
                        },
                      },
                      input: {
                        borderWidth: '1px',
                      },
                      innerInput: {
                        fontSize: '16px',
                        color: '#3F475A',
                      },
                    };
                }}
                visibilityToggleIcon={({ reveal }) =>
                  reveal ? (
                    <FontAwesomeIcon
                      icon={faEyeSlash}
                      className="text-textSecondary hover:text-textPrimary"
                    />
                  ) : (
                    <FontAwesomeIcon
                      icon={faEye}
                      className="text-textSecondary hover:text-textPrimary"
                    />
                  )
                }
                {...form.getInputProps('password')}
              />
              <Link
                field={{
                  value: {
                    href: `${props.fields.ForgotPasswordLink.value.href}`,
                  },
                }}
                // target="_blank"
                className="text-textSecondary hover:text-textPrimary text-minus1 font-primaryBold block"
              >
                {props.fields.ForgotPassword.value}
              </Link>
              <TextInput
                className="hidden"
                label={props.fields.UserName.value}
                styles={(theme) => ({
                  root: {
                    width: '100%',
                    ['@media (min-width: 1024px)']: {
                      width: '100%',
                    },
                  },
                  label: {
                    fontFamily: 'OpenSans-Regular',
                    fontSize: '14px',
                    color: '#414042',
                  },
                  error: {
                    fontFamily: 'OpenSans-Regular',
                    fontSize: '12px',
                  },
                  input: {
                    border: `1px solid ${theme.other.colors.borderSecondary[0]}`,
                    borderRadius: '4px',
                    height: '52px',
                  },
                })}
                {...form.getInputProps('username')}
              />

              <Link
                field={{
                  value: {
                    href: `${props.fields.ForgotUserNameLink.value.href}`,
                  },
                }}
                // target="_blank"
                className="text-textSecondary hover:text-textPrimary text-minus3 font-primaryBold hidden"
              >
                {props.fields.ForgotUserName.value}
              </Link>
              <PasswordInput
                className="hidden"
                label={props.fields.Password.value}
                styles={(theme) => ({
                  root: {
                    width: '100%',
                    ['@media (min-width: 1024px)']: {
                      width: '100%',
                    },
                  },
                  label: {
                    fontFamily: 'OpenSans-Regular',
                    fontSize: '14px',
                    color: '#414042',
                  },
                  input: {
                    border: `1px solid ${theme.other.colors.borderSecondary[0]}`,
                    borderRadius: '4px',
                    height: '52px',
                  },
                  error: {
                    fontFamily: 'OpenSans-Regular',
                    fontSize: '12px',
                  },
                  innerInput: {
                    fontSize: '16px',
                  },
                })}
                visibilityToggleIcon={({ reveal }) =>
                  reveal ? (
                    <FontAwesomeIcon
                      icon={faEyeSlash}
                      className="text-textSecondary hover:textPrimary"
                    />
                  ) : (
                    <FontAwesomeIcon
                      icon={faEye}
                      className="text-textSecondary hover:textPrimary"
                    />
                  )
                }
                {...form.getInputProps('password')}
              />
              <Link
                field={{
                  value: {
                    href: `${props.fields.ForgotPasswordLink.value.href}`,
                  },
                }}
                //  target="_blank"
                className="text-textSecondary hover:text-textPrimary text-minus3 font-primaryBold hidden"
              >
                {props.fields.ForgotPassword.value}
              </Link>
              <Checkbox
                className="my-4"
                radius="xs"
                size="xs"
                checked={rememberMe}
                onChange={() => setRememberMe(!rememberMe)}
                label={props.fields.RememberMe.value}
                styles={{
                  label: {
                    fontFamily: 'OpenSans-Regular',
                    fontSize: '16px',
                    color: '#353434',
                  },
                }}
              />

              <Button
                className="w-[160px] px-6"
                icon={
                  loading ? (
                    <FontAwesomeIcon icon={faSpinner} className="text-textQuinary" size="xs" spin />
                  ) : (
                    <>
                      <FontAwesomeIcon icon={faArrowRight} className="" />
                      <FontAwesomeIcon icon={faChevronsRight} className="hidden text-minus2" />
                    </>
                  )
                }
                disabled={loading}
              >
                {props.fields.SignIn.value}
              </Button>

              <RichText
                tag="p"
                className="font-primaryBold text-minus1 text-error"
                field={{ value: Array.isArray(errorMsg) ? errorMsg.join(' ') : errorMsg || '' }}
              />
            </form>
            <div className="border-t-[1.5px] border-borderQuattuordenary mt-12 pt-12 sm:hidden">
              <RichText
                field={{
                  value: String(props.fields.NewCustomerText.value),
                }}
                className="text-textSeptenary sm:text-minus1 font-primaryRegular pb-2"
              />
              {props.fields.CreateAnAccount?.value && (
                <Link
                  field={{
                    value: {
                      href: `${props.fields.CreateAccountUrl.value.href}`,
                    },
                  }}
                  //  target="_blank"
                  className="hover:text-textPrimary text-textSecondary sm:text-minus1 text-minus3 underline underline-offset-4 decaration-2  hover:decoration-hoverPrimary decoration-hoverPrimary font-primaryRegular"
                >
                  {props.fields.CreateAnAccount.value}
                  <FontAwesomeIcon icon={faChevronsRight} className="text-minus4 pl-2" />
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export { Login };
const Component = withDatasourceCheck()<LoginProps>(Login);
export default aiLogger(Component, Component.name);
