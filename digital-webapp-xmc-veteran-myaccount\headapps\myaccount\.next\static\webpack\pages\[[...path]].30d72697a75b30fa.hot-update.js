"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/COA/logininfo/Logininformation.tsx":
/*!***********************************************************!*\
  !*** ./src/components/COA/logininfo/Logininformation.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CombinedComponent: function() { return /* binding */ CombinedComponent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @fortawesome/pro-light-svg-icons */ \"./node_modules/@fortawesome/pro-light-svg-icons/index.mjs\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"./node_modules/@mantine/core/esm/index.js\");\n/* harmony import */ var _mantine_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/form */ \"./node_modules/@mantine/form/esm/index.js\");\n/* harmony import */ var src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/hoc/ApplicationInsightsLogger */ \"./src/hoc/ApplicationInsightsLogger.tsx\");\n/* harmony import */ var components_Elements_Button_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/Elements/Button/Button */ \"./src/components/Elements/Button/Button.tsx\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var src_stores_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/stores/store */ \"./src/stores/store.ts\");\n/* harmony import */ var src_stores_coaSlice__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/stores/coaSlice */ \"./src/stores/coaSlice.ts\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var _fortawesome_pro_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/pro-regular-svg-icons */ \"./node_modules/@fortawesome/pro-regular-svg-icons/index.mjs\");\n/* harmony import */ var lib_app_insights_log_error__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lib/app-insights-log-error */ \"./src/lib/app-insights-log-error.ts\");\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n // Import useForm from Mantine\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst CombinedComponent = (props)=>{\r\n    var _props_fields_TermsConditionsURL, _props_fields, _props_fields_AcceptedCheckBoxText, _props_fields1, _props_fields_TermsConditions, _props_fields2, _props_fields_rxUsername_value, _props_fields_rxUsername, _props_fields3, _props_fields_BillingConsentToolTipText_value, _props_fields_BillingConsentToolTipText, _props_fields4, _props_fields_VerifyYourInfoText, _props_fields5, _props_fields_UsernameText, _props_fields6, _props_fields_EmailRequirementsText, _props_fields7, _props_fields_PasswordText, _props_fields8, _props_fields_PasswordCharacterLimitText, _props_fields9, _props_fields_ConfirmPasswordText, _props_fields10, _props_fields_SecurityQuestionText, _props_fields11, _props_fields12, _props_fields_SecurityQuestions, _props_fields13, _props_fields_SpecifyAnswerText, _props_fields14, _props_fields_YesCheckBoxText, _props_fields15, _props_fields_YesCheckBoxText1, _props_fields16, _props_fields17, _props_fields_WhyPaperlessBillingTitleText, _props_fields18, _props_fields_WhyPaperlessBillingTitleText1, _props_fields19, _props_fields20, _props_fields21, _props_fields22, _props_fields_SubmitButtonText, _props_fields23, _props_fields_CancelButtonText, _props_fields24;\r\n    _s();\r\n    const context = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.useSitecoreContext)();\r\n    const isPageEditing = context.sitecoreContext.pageEditing;\r\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const [isAcceptedChecked, setIsAcceptedChecked] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\r\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const [showSecurityAnswer, setShowSecurityAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const [isYesCheckedForSpecialOffer, setIsYesCheckedForSpecialOffer] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\r\n    let accountInfo = undefined;\r\n    let dispatch;\r\n    if (!isPageEditing) {\r\n        accountInfo = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_6__.useAppSelector)((state)=>{\r\n            var _state_coa;\r\n            return state === null || state === void 0 ? void 0 : (_state_coa = state.coa) === null || _state_coa === void 0 ? void 0 : _state_coa.accountInfo;\r\n        });\r\n        dispatch = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_6__.useAppDispatch)();\r\n    }\r\n    const termsUrl = ((_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_TermsConditionsURL = _props_fields.TermsConditionsURL) === null || _props_fields_TermsConditionsURL === void 0 ? void 0 : _props_fields_TermsConditionsURL.value) || \"#\";\r\n    const acceptText = ((_props_fields1 = props.fields) === null || _props_fields1 === void 0 ? void 0 : (_props_fields_AcceptedCheckBoxText = _props_fields1.AcceptedCheckBoxText) === null || _props_fields_AcceptedCheckBoxText === void 0 ? void 0 : _props_fields_AcceptedCheckBoxText.value) || \"I accept the\";\r\n    const termsText = ((_props_fields2 = props.fields) === null || _props_fields2 === void 0 ? void 0 : (_props_fields_TermsConditions = _props_fields2.TermsConditions) === null || _props_fields_TermsConditions === void 0 ? void 0 : _props_fields_TermsConditions.value) || \"Terms and Conditions\";\r\n    const regexUsername = ((_props_fields3 = props.fields) === null || _props_fields3 === void 0 ? void 0 : (_props_fields_rxUsername = _props_fields3.rxUsername) === null || _props_fields_rxUsername === void 0 ? void 0 : (_props_fields_rxUsername_value = _props_fields_rxUsername.value) === null || _props_fields_rxUsername_value === void 0 ? void 0 : _props_fields_rxUsername_value.slice(1, -1)) || \"\";\r\n    const [isPaperlessChecked, setIsPaperlessChecked] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)((accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.isCommunicationMethodEmail) === true ? true : false);\r\n    const showToolTip = ((_props_fields4 = props.fields) === null || _props_fields4 === void 0 ? void 0 : (_props_fields_BillingConsentToolTipText = _props_fields4.BillingConsentToolTipText) === null || _props_fields_BillingConsentToolTipText === void 0 ? void 0 : (_props_fields_BillingConsentToolTipText_value = _props_fields_BillingConsentToolTipText.value) === null || _props_fields_BillingConsentToolTipText_value === void 0 ? void 0 : _props_fields_BillingConsentToolTipText_value.trim().length) > 0;\r\n    const storedEmail = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_6__.useAppSelector)((state)=>{\r\n        var _state_coa_accountInfo, _state_coa;\r\n        return state === null || state === void 0 ? void 0 : (_state_coa = state.coa) === null || _state_coa === void 0 ? void 0 : (_state_coa_accountInfo = _state_coa.accountInfo) === null || _state_coa_accountInfo === void 0 ? void 0 : _state_coa_accountInfo.email;\r\n    });\r\n    const [isUsernameDisabled, setIsUsernameDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\r\n    // Initialize the form with useForm\r\n    const form = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\r\n        initialValues: {\r\n            username: \"\",\r\n            password: \"\",\r\n            confirmPassword: \"\",\r\n            securityQuestion: \"\",\r\n            securityAnswer: \"\"\r\n        },\r\n        validate: {\r\n            username: (value)=>{\r\n                var _props_fields_InvalidEmailValidationText, _props_fields;\r\n                const isValidUserName = new RegExp(regexUsername).test(value);\r\n                return isValidUserName ? null : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_InvalidEmailValidationText = _props_fields.InvalidEmailValidationText) === null || _props_fields_InvalidEmailValidationText === void 0 ? void 0 : _props_fields_InvalidEmailValidationText.value;\r\n            },\r\n            password: (value)=>{\r\n                var _props_fields_rxPassword, _props_fields, _props_fields_PasswordCharactersValidationText, _props_fields1;\r\n                const regexPattern = props === null || props === void 0 ? void 0 : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_rxPassword = _props_fields.rxPassword) === null || _props_fields_rxPassword === void 0 ? void 0 : _props_fields_rxPassword.value;\r\n                const regex = new RegExp(regexPattern).test(value);\r\n                const isValidPassword = regex || /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^]).{12,}$/.test(value);\r\n                return isValidPassword ? null : (_props_fields1 = props.fields) === null || _props_fields1 === void 0 ? void 0 : (_props_fields_PasswordCharactersValidationText = _props_fields1.PasswordCharactersValidationText) === null || _props_fields_PasswordCharactersValidationText === void 0 ? void 0 : _props_fields_PasswordCharactersValidationText.value;\r\n            },\r\n            confirmPassword: (value, values)=>{\r\n                var _props_fields_PasswordsDoNotMatch, _props_fields;\r\n                const doPasswordsMatch = value === values.password;\r\n                return doPasswordsMatch ? null : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_PasswordsDoNotMatch = _props_fields.PasswordsDoNotMatch) === null || _props_fields_PasswordsDoNotMatch === void 0 ? void 0 : _props_fields_PasswordsDoNotMatch.value;\r\n            },\r\n            securityQuestion: (value)=>{\r\n                var _props_fields_SelectQuestionText, _props_fields;\r\n                const isQuestionSelected = !!value;\r\n                return isQuestionSelected ? null : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_SelectQuestionText = _props_fields.SelectQuestionText) === null || _props_fields_SelectQuestionText === void 0 ? void 0 : _props_fields_SelectQuestionText.value;\r\n            },\r\n            securityAnswer: (value)=>{\r\n                var _props_fields_AnswerValidationText, _props_fields;\r\n                const isAnswerValid = value.length >= 3;\r\n                return isAnswerValid ? null : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_AnswerValidationText = _props_fields.AnswerValidationText) === null || _props_fields_AnswerValidationText === void 0 ? void 0 : _props_fields_AnswerValidationText.value;\r\n            }\r\n        },\r\n        validateInputOnBlur: true\r\n    });\r\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\r\n        const fetchUserByEmail = async ()=>{\r\n            if (!storedEmail) return;\r\n            try {\r\n                var _response_data;\r\n                const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get(\"/api/forgotpassword/checkusername?username=\".concat(storedEmail));\r\n                const apiResult = response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.result;\r\n                form.setFieldValue(\"username\", !apiResult ? storedEmail : \"\");\r\n                setIsUsernameDisabled(!apiResult);\r\n            } catch (error) {\r\n                var _err_response;\r\n                const err = error;\r\n                (0,lib_app_insights_log_error__WEBPACK_IMPORTED_MODULE_8__.logErrorToAppInsights)(err, {\r\n                    componentStack: \"Login\"\r\n                });\r\n                console.error(\"Error fetching user by email:\", (_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data);\r\n            }\r\n        };\r\n        fetchUserByEmail();\r\n    }, []);\r\n    const handleSubmit = async ()=>{\r\n        if (!isAcceptedChecked) {\r\n            var _props_fields_AgreeTC, _props_fields;\r\n            setErrorMessage((_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_AgreeTC = _props_fields.AgreeTC) === null || _props_fields_AgreeTC === void 0 ? void 0 : _props_fields_AgreeTC.value);\r\n            return;\r\n        }\r\n        if (!form.validate().hasErrors) {\r\n            setErrorMessage(\"\");\r\n            setLoading(true);\r\n            var _accountInfo_organizationBpNumber;\r\n            const requestBody = {\r\n                Username: form.values.username,\r\n                Email: accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.email,\r\n                SecurityQuestion: form.values.securityQuestion,\r\n                SecurityAnswer: form.values.securityAnswer,\r\n                CustomerClassification: accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.customerClassification,\r\n                EnrollmentChannel: 1,\r\n                FirstName: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.firstName) || \"\",\r\n                LastName: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.lastName) || \"\",\r\n                LanguagePreference: \"English\",\r\n                OrganizationBPNumber: (_accountInfo_organizationBpNumber = accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.organizationBpNumber) !== null && _accountInfo_organizationBpNumber !== void 0 ? _accountInfo_organizationBpNumber : null,\r\n                Password: form.values.password,\r\n                PaperlessBilling: isPaperlessChecked,\r\n                SpecialOffers: isYesCheckedForSpecialOffer,\r\n                PersonBPNumber: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.partnerNumber) || \"\",\r\n                SendEmailInvitation: true,\r\n                isBusinessUser: accountInfo && accountInfo.customerClassification.toString() == \"3\" ? false : true,\r\n                AccountNumber: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.accountNumber) || \"\",\r\n                Portal: accountInfo && accountInfo.customerClassification.toString() == \"3\" ? 4 : 5,\r\n                isEnrollment: true\r\n            };\r\n            try {\r\n                setLoading(true);\r\n                const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post(\"/api/createonlineaccount/create\", requestBody);\r\n                if (response.data.result) {\r\n                    var _props_fields_verifyinformationurl;\r\n                    if (!isPageEditing) {\r\n                        dispatch((0,src_stores_coaSlice__WEBPACK_IMPORTED_MODULE_7__.setAccountInfo)({\r\n                            ...accountInfo,\r\n                            email: form.values.username,\r\n                            accountNumber: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.accountNumber) || \"\",\r\n                            firstName: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.firstName) || \"\",\r\n                            lastName: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.lastName) || \"\",\r\n                            partnerNumber: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.partnerNumber) || \"\",\r\n                            isCommunicationMethodEmail: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.isCommunicationMethodEmail) === true ? true : false,\r\n                            customerClassification: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.customerClassification) || 4,\r\n                            organizationBpNumber: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.organizationBpNumber) || \"\",\r\n                            isBusiness: (accountInfo === null || accountInfo === void 0 ? void 0 : accountInfo.isBusiness) || false\r\n                        }));\r\n                        dispatch((0,src_stores_coaSlice__WEBPACK_IMPORTED_MODULE_7__.setSecurityQuestion)(form.values.securityQuestion));\r\n                        dispatch((0,src_stores_coaSlice__WEBPACK_IMPORTED_MODULE_7__.setSecurityAnswer)(form.values.securityAnswer));\r\n                    }\r\n                    router.push((_props_fields_verifyinformationurl = props.fields.verifyinformationurl) === null || _props_fields_verifyinformationurl === void 0 ? void 0 : _props_fields_verifyinformationurl.value.href); //'/coa/Verify-Login-Information');\r\n                } else {\r\n                    var _response_data;\r\n                    setLoading(false);\r\n                    let errMsg;\r\n                    if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.messages[0]) {\r\n                        var _response_data1;\r\n                        if (((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.messages[0]) === \"Username Duplicate Message\") {\r\n                            var _props_fields1;\r\n                            errMsg = (_props_fields1 = props.fields) === null || _props_fields1 === void 0 ? void 0 : _props_fields1.LoginEmailAlreadyExist.value;\r\n                        } else {\r\n                            var _response_data2;\r\n                            errMsg = (_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.messages[0];\r\n                        }\r\n                        setErrorMessage(errMsg);\r\n                    } else {\r\n                        errMsg = props.fields.GenericeErrorMessage.value;\r\n                        setErrorMessage(errMsg);\r\n                    }\r\n                }\r\n            } catch (error) {\r\n                var _props_fields_problemCreatingAccount;\r\n                setLoading(false);\r\n                setErrorMessage((_props_fields_problemCreatingAccount = props.fields.problemCreatingAccount) === null || _props_fields_problemCreatingAccount === void 0 ? void 0 : _props_fields_problemCreatingAccount.value);\r\n            } finally{\r\n                setLoading(false);\r\n            }\r\n        }\r\n    };\r\n    const handleCancel = async ()=>{\r\n        var _props_fields_accountinformationurl;\r\n        if (!isPageEditing) {\r\n            dispatch((0,src_stores_coaSlice__WEBPACK_IMPORTED_MODULE_7__.clearAccountInfo)());\r\n        }\r\n        router.push((_props_fields_accountinformationurl = props.fields.accountinformationurl) === null || _props_fields_accountinformationurl === void 0 ? void 0 : _props_fields_accountinformationurl.value.href); //'coa/account-information');\r\n    };\r\n    var _props_fields_SecurityQuestions_map;\r\n    //console.log('SecurityQuestions= ' + props.fields.SecurityQuestions); // Debugging line to check the data\r\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n            className: \"w-full sm:max-w-[1040px] my-10 m-auto text-start\",\r\n            children: [\r\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.Text, {\r\n                    tag: \"h1\",\r\n                    field: {\r\n                        value: (_props_fields5 = props.fields) === null || _props_fields5 === void 0 ? void 0 : (_props_fields_VerifyYourInfoText = _props_fields5.VerifyYourInfoText) === null || _props_fields_VerifyYourInfoText === void 0 ? void 0 : _props_fields_VerifyYourInfoText.value\r\n                    },\r\n                    className: \"sm:text-plus3 text-base sm:mr-auto pb-6 font-primaryBold text-textUndenary\"\r\n                }, void 0, false, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                    lineNumber: 272,\r\n                    columnNumber: 9\r\n                }, undefined),\r\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.Text, {\r\n                    className: \"font-primaryRegular text-textUndenary text-base\",\r\n                    tag: \"p\",\r\n                    field: {\r\n                        value: \"Enter an email and password to be associated with this account.\"\r\n                    }\r\n                }, void 0, false, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                    lineNumber: 277,\r\n                    columnNumber: 9\r\n                }, undefined),\r\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                    children: [\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"flex flex-row w-full gap-20 justify-start mt-5\",\r\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                className: \"w-full px-4 sm:px-0 sm:max-w-[500px]\",\r\n                                children: [\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex py-2 justify-between flex-col items-start gap-2\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"w-[21rem] flex text-left items-center md:w-full  \".concat(form.errors.username ? \"mb-[30px]\" : \"\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 292,\r\n                                                columnNumber: 17\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"flex flex-col\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\r\n                                                        label: (_props_fields6 = props.fields) === null || _props_fields6 === void 0 ? void 0 : (_props_fields_UsernameText = _props_fields6.UsernameText) === null || _props_fields_UsernameText === void 0 ? void 0 : _props_fields_UsernameText.value,\r\n                                                        ...form.getInputProps(\"username\"),\r\n                                                        autoComplete: \"new-password\",\r\n                                                        disabled: isUsernameDisabled,\r\n                                                        styles: {\r\n                                                            input: {\r\n                                                                textAlign: \"left\",\r\n                                                                width: \"21rem\"\r\n                                                            },\r\n                                                            error: {\r\n                                                                maxWidth: \"21rem\",\r\n                                                                wordWrap: \"break-word\"\r\n                                                            }\r\n                                                        }\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 298,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.Text, {\r\n                                                        tag: \"p\",\r\n                                                        field: {\r\n                                                            value: (_props_fields7 = props.fields) === null || _props_fields7 === void 0 ? void 0 : (_props_fields_EmailRequirementsText = _props_fields7.EmailRequirementsText) === null || _props_fields_EmailRequirementsText === void 0 ? void 0 : _props_fields_EmailRequirementsText.value\r\n                                                        },\r\n                                                        className: \"font-primaryRegular text-textUndenary mt-2 text-minus2 sm:text-minus1 text-start md:text-left sm:mr-auto\"\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 308,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 297,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                        lineNumber: 291,\r\n                                        columnNumber: 15\r\n                                    }, undefined),\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex py-2 justify-between flex-col items-start gap-2\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"w-[21rem] flex items-center md:w-full\"\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 317,\r\n                                                columnNumber: 17\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"relative\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\r\n                                                        label: (_props_fields8 = props.fields) === null || _props_fields8 === void 0 ? void 0 : (_props_fields_PasswordText = _props_fields8.PasswordText) === null || _props_fields_PasswordText === void 0 ? void 0 : _props_fields_PasswordText.value,\r\n                                                        ...form.getInputProps(\"password\"),\r\n                                                        type: showPassword ? \"text\" : \"password\",\r\n                                                        autoComplete: \"new-password\",\r\n                                                        styles: {\r\n                                                            input: {\r\n                                                                textAlign: \"left\",\r\n                                                                width: \"21rem\"\r\n                                                            },\r\n                                                            error: {\r\n                                                                maxWidth: \"21rem\",\r\n                                                                wordWrap: \"break-word\"\r\n                                                            }\r\n                                                        }\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 319,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                        icon: showPassword ? _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faEyeSlash : _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faEye,\r\n                                                        className: \"absolute right-[25px] sm:right-[85px] top-[50px] cursor-pointer text-textPrimary\",\r\n                                                        onClick: ()=>setShowPassword(!showPassword)\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 329,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.Text, {\r\n                                                        tag: \"p\",\r\n                                                        field: {\r\n                                                            value: (_props_fields9 = props.fields) === null || _props_fields9 === void 0 ? void 0 : (_props_fields_PasswordCharacterLimitText = _props_fields9.PasswordCharacterLimitText) === null || _props_fields_PasswordCharacterLimitText === void 0 ? void 0 : _props_fields_PasswordCharacterLimitText.value\r\n                                                        },\r\n                                                        className: \"mt-2 font-primaryRegular text-textUndenary text-start md:text-left sm:mr-auto text-minus2 sm:text-minus1\"\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 334,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 318,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                        lineNumber: 316,\r\n                                        columnNumber: 15\r\n                                    }, undefined),\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex py-2 justify-between flex-col items-start gap-2\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"w-[21rem] flex flex-col md:w-full \".concat(form.errors.confirmPassword ? \"mb-[30px]\" : \"\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 343,\r\n                                                columnNumber: 17\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"relative\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\r\n                                                        ...form.getInputProps(\"confirmPassword\"),\r\n                                                        label: (_props_fields10 = props.fields) === null || _props_fields10 === void 0 ? void 0 : (_props_fields_ConfirmPasswordText = _props_fields10.ConfirmPasswordText) === null || _props_fields_ConfirmPasswordText === void 0 ? void 0 : _props_fields_ConfirmPasswordText.value,\r\n                                                        type: showConfirmPassword ? \"text\" : \"password\",\r\n                                                        styles: {\r\n                                                            input: {\r\n                                                                textAlign: \"left\",\r\n                                                                width: \"21rem\"\r\n                                                            },\r\n                                                            error: {\r\n                                                                maxWidth: \"21rem\",\r\n                                                                wordWrap: \"break-word\"\r\n                                                            }\r\n                                                        }\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 349,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                        icon: showConfirmPassword ? _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faEyeSlash : _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faEye,\r\n                                                        className: \"absolute right-[16px] top-[50px] cursor-pointer text-textPrimary\",\r\n                                                        onClick: ()=>setShowConfirmPassword(!showConfirmPassword)\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 358,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 348,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                        lineNumber: 342,\r\n                                        columnNumber: 15\r\n                                    }, undefined),\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex py-2 justify-between flex-col items-start gap-2\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"w-[21rem] flex items-center md:w-full flex-col\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.Text, {\r\n                                                        tag: \"h2\",\r\n                                                        field: {\r\n                                                            value: (_props_fields11 = props.fields) === null || _props_fields11 === void 0 ? void 0 : (_props_fields_SecurityQuestionText = _props_fields11.SecurityQuestionText) === null || _props_fields_SecurityQuestionText === void 0 ? void 0 : _props_fields_SecurityQuestionText.value\r\n                                                        },\r\n                                                        className: \"font-primaryBold text-textUndenary text-plus3 mt-5 sm:text-[32px] text-left mr-auto w-full\"\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 368,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.Text, {\r\n                                                        tag: \"p\",\r\n                                                        field: (_props_fields12 = props.fields) === null || _props_fields12 === void 0 ? void 0 : _props_fields12.SelectSecurityQuestionText,\r\n                                                        className: \"font-primaryRegular text-textUndenary text-base sm:text-minus1 text-left mr-auto pt-3 pb-2\"\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 373,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 367,\r\n                                                columnNumber: 17\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Select, {\r\n                                                className: \"w-[21rem] text-[16px] text-base mt-5 font-primaryRegular text-textUndenary tracking-normal leading-[24px]\",\r\n                                                placeholder: \"Choose a security question\",\r\n                                                styles: {\r\n                                                    root: {\r\n                                                        width: \"21rem\",\r\n                                                        [\"@media (max-width: 767px)\"]: {\r\n                                                            width: \"100%\"\r\n                                                        }\r\n                                                    }\r\n                                                },\r\n                                                ...form.getInputProps(\"securityQuestion\"),\r\n                                                data: (_props_fields_SecurityQuestions_map = props === null || props === void 0 ? void 0 : (_props_fields13 = props.fields) === null || _props_fields13 === void 0 ? void 0 : (_props_fields_SecurityQuestions = _props_fields13.SecurityQuestions) === null || _props_fields_SecurityQuestions === void 0 ? void 0 : _props_fields_SecurityQuestions.map((securityquestion)=>{\r\n                                                    var _securityquestion_fields_Title, _securityquestion_fields;\r\n                                                    return securityquestion === null || securityquestion === void 0 ? void 0 : (_securityquestion_fields = securityquestion.fields) === null || _securityquestion_fields === void 0 ? void 0 : (_securityquestion_fields_Title = _securityquestion_fields.Title) === null || _securityquestion_fields_Title === void 0 ? void 0 : _securityquestion_fields_Title.value;\r\n                                                })) !== null && _props_fields_SecurityQuestions_map !== void 0 ? _props_fields_SecurityQuestions_map : [],\r\n                                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                    icon: _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faChevronDown,\r\n                                                    className: \"text-textPrimary\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 397,\r\n                                                    columnNumber: 21\r\n                                                }, void 0)\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 379,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                        lineNumber: 366,\r\n                                        columnNumber: 15\r\n                                    }, undefined),\r\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                        className: \"flex py-2 justify-between flex-col items-start gap-2\",\r\n                                        children: [\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"w-[21rem] flex items-center md:w-full font-primaryBold \".concat(form.errors.securityAnswer ? \"mb-[30px]\" : \"\")\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 402,\r\n                                                columnNumber: 17\r\n                                            }, undefined),\r\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                className: \"relative\",\r\n                                                children: [\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\r\n                                                        label: (_props_fields14 = props.fields) === null || _props_fields14 === void 0 ? void 0 : (_props_fields_SpecifyAnswerText = _props_fields14.SpecifyAnswerText) === null || _props_fields_SpecifyAnswerText === void 0 ? void 0 : _props_fields_SpecifyAnswerText.value,\r\n                                                        ...form.getInputProps(\"securityAnswer\"),\r\n                                                        disabled: !form.values.securityQuestion,\r\n                                                        type: showSecurityAnswer ? \"text\" : \"password\",\r\n                                                        styles: {\r\n                                                            input: {\r\n                                                                textAlign: \"left\",\r\n                                                                width: \"21rem\"\r\n                                                            },\r\n                                                            error: {\r\n                                                                maxWidth: \"21rem\",\r\n                                                                wordWrap: \"break-word\",\r\n                                                                paddingLeft: \"0\"\r\n                                                            }\r\n                                                        }\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 408,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined),\r\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                        icon: showSecurityAnswer ? _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faEyeSlash : _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faEye,\r\n                                                        className: \"absolute right-[16px] top-[50px] cursor-pointer text-textPrimary\",\r\n                                                        onClick: ()=>setShowSecurityAnswer(!showSecurityAnswer)\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 418,\r\n                                                        columnNumber: 19\r\n                                                    }, undefined)\r\n                                                ]\r\n                                            }, void 0, true, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 407,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        ]\r\n                                    }, void 0, true, {\r\n                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                        lineNumber: 401,\r\n                                        columnNumber: 15\r\n                                    }, undefined)\r\n                                ]\r\n                            }, void 0, true, {\r\n                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                lineNumber: 289,\r\n                                columnNumber: 13\r\n                            }, undefined)\r\n                        }, void 0, false, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                            lineNumber: 288,\r\n                            columnNumber: 11\r\n                        }, undefined),\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"w-full px-4 sm:px-0 sm:max-w-[500px] m-0\",\r\n                            children: [\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                    className: \"flex flex-col justify-center my-6 \",\r\n                                    children: [\r\n                                        ((_props_fields15 = props.fields) === null || _props_fields15 === void 0 ? void 0 : (_props_fields_YesCheckBoxText = _props_fields15.YesCheckBoxText) === null || _props_fields_YesCheckBoxText === void 0 ? void 0 : _props_fields_YesCheckBoxText.value) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            className: \"flex flex-row py-2\",\r\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Checkbox, {\r\n                                                classNames: {\r\n                                                    input: \"customChecked\"\r\n                                                },\r\n                                                checked: isYesCheckedForSpecialOffer,\r\n                                                onChange: (event)=>{\r\n                                                    setIsYesCheckedForSpecialOffer(event.currentTarget.checked);\r\n                                                },\r\n                                                styles: {\r\n                                                    body: {\r\n                                                        display: \"flex\",\r\n                                                        alignItems: \"center\"\r\n                                                    }\r\n                                                },\r\n                                                radius: \"xs\",\r\n                                                label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                    className: \"flex flex-wrap items-center text-minus1 text-textQuattuordenary text-left\",\r\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\r\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.RichText, {\r\n                                                            field: {\r\n                                                                value: (_props_fields16 = props.fields) === null || _props_fields16 === void 0 ? void 0 : (_props_fields_YesCheckBoxText1 = _props_fields16.YesCheckBoxText) === null || _props_fields_YesCheckBoxText1 === void 0 ? void 0 : _props_fields_YesCheckBoxText1.value\r\n                                                            }\r\n                                                        }, void 0, false, {\r\n                                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                            lineNumber: 447,\r\n                                                            columnNumber: 27\r\n                                                        }, void 0)\r\n                                                    }, void 0, false)\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 445,\r\n                                                    columnNumber: 23\r\n                                                }, void 0)\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 431,\r\n                                                columnNumber: 19\r\n                                            }, undefined)\r\n                                        }, void 0, false, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                            lineNumber: 430,\r\n                                            columnNumber: 17\r\n                                        }, undefined),\r\n                                        ((_props_fields17 = props.fields) === null || _props_fields17 === void 0 ? void 0 : _props_fields17.WhyPaperlessBillingTitleText) && ((_props_fields18 = props.fields) === null || _props_fields18 === void 0 ? void 0 : (_props_fields_WhyPaperlessBillingTitleText = _props_fields18.WhyPaperlessBillingTitleText) === null || _props_fields_WhyPaperlessBillingTitleText === void 0 ? void 0 : _props_fields_WhyPaperlessBillingTitleText.value) !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\r\n                                            type: \"button\",\r\n                                            onClick: ()=>setShowDetails((val)=>!val),\r\n                                            className: \"flex items-center font-primaryRegular text-textUndenary mt-3 text-minus1 hover:text-textSecondary -tracking-[0.25px] cursor-pointer justify-start\",\r\n                                            children: [\r\n                                                (_props_fields19 = props.fields) === null || _props_fields19 === void 0 ? void 0 : (_props_fields_WhyPaperlessBillingTitleText1 = _props_fields19.WhyPaperlessBillingTitleText) === null || _props_fields_WhyPaperlessBillingTitleText1 === void 0 ? void 0 : _props_fields_WhyPaperlessBillingTitleText1.value,\r\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                    icon: showDetails ? _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faChevronUp : _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faChevronDown,\r\n                                                    className: \"font-primaryRegular text-minus2 pl-2\",\r\n                                                    size: \"xs\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 498,\r\n                                                    columnNumber: 21\r\n                                                }, undefined)\r\n                                            ]\r\n                                        }, void 0, true, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                            lineNumber: 492,\r\n                                            columnNumber: 19\r\n                                        }, undefined),\r\n                                        showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\r\n                                            children: [\r\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.RichText, {\r\n                                                    tag: \"p\",\r\n                                                    field: props === null || props === void 0 ? void 0 : (_props_fields20 = props.fields) === null || _props_fields20 === void 0 ? void 0 : _props_fields20.BenefitsofPaperlessText,\r\n                                                    className: \"pt-2 flex font-primaryRegular text-textUndenary text-minus1\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 507,\r\n                                                    columnNumber: 19\r\n                                                }, undefined),\r\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.RichText, {\r\n                                                    tag: \"p\",\r\n                                                    field: props === null || props === void 0 ? void 0 : (_props_fields21 = props.fields) === null || _props_fields21 === void 0 ? void 0 : _props_fields21.PaperlessBillingDropDownText,\r\n                                                    className: \"flex font-primaryRegular text-textUndenary mb-5 text-minus1\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 512,\r\n                                                    columnNumber: 19\r\n                                                }, undefined)\r\n                                            ]\r\n                                        }, void 0, true),\r\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            className: \"flex flex-row py-2\",\r\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Checkbox, {\r\n                                                checked: isAcceptedChecked,\r\n                                                onChange: (event)=>{\r\n                                                    const checked = event.currentTarget.checked;\r\n                                                    setIsAcceptedChecked(checked);\r\n                                                    if (checked) {\r\n                                                        setErrorMessage(\"\");\r\n                                                    }\r\n                                                },\r\n                                                classNames: {\r\n                                                    input: \"customChecked\"\r\n                                                },\r\n                                                styles: {\r\n                                                    body: {\r\n                                                        display: \"flex\",\r\n                                                        alignItems: \"center\"\r\n                                                    }\r\n                                                },\r\n                                                radius: \"xs\",\r\n                                                label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                                    className: \"flex flex-wrap items-center text-minus1 text-textQuattuordenary text-left\",\r\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.RichText, {\r\n                                                        tag: \"p\",\r\n                                                        field: props === null || props === void 0 ? void 0 : (_props_fields22 = props.fields) === null || _props_fields22 === void 0 ? void 0 : _props_fields22.TermsConditions,\r\n                                                        className: \"pt-2 font-primaryRegular sm:flex text-textUndenary text-minus1 page-link\"\r\n                                                    }, void 0, false, {\r\n                                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                        lineNumber: 540,\r\n                                                        columnNumber: 23\r\n                                                    }, void 0)\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 539,\r\n                                                    columnNumber: 21\r\n                                                }, void 0)\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 520,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        }, void 0, false, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                            lineNumber: 519,\r\n                                            columnNumber: 15\r\n                                        }, undefined)\r\n                                    ]\r\n                                }, void 0, true, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                    lineNumber: 428,\r\n                                    columnNumber: 13\r\n                                }, undefined),\r\n                                errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                    className: \"bg-red-100 border border-red-400 text-textDenary px-4 py-3 rounded relative\",\r\n                                    role: \"alert\",\r\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\r\n                                        className: \"block sm:inline\",\r\n                                        children: errorMessage\r\n                                    }, void 0, false, {\r\n                                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                        lineNumber: 555,\r\n                                        columnNumber: 17\r\n                                    }, undefined)\r\n                                }, void 0, false, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                    lineNumber: 551,\r\n                                    columnNumber: 15\r\n                                }, undefined),\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                    className: \"flex flex-col sm:flex-col py-2 justify-start sm:items-start items-center gap-6 mt-8\",\r\n                                    children: [\r\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_Elements_Button_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\r\n                                                className: \"sm:w-[240px] px-6 h-[56px] m-0 w-[240px]\",\r\n                                                icon: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                    icon: _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faSpinner,\r\n                                                    className: \"text-textQuinary\",\r\n                                                    size: \"xs\",\r\n                                                    spin: true\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 564,\r\n                                                    columnNumber: 23\r\n                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\r\n                                                    children: [\r\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                            icon: _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faArrowRight,\r\n                                                            className: \"hidden\"\r\n                                                        }, void 0, false, {\r\n                                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                            lineNumber: 572,\r\n                                                            columnNumber: 25\r\n                                                        }, void 0),\r\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                            icon: _fortawesome_pro_light_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faChevronsRight,\r\n                                                            className: \"block\"\r\n                                                        }, void 0, false, {\r\n                                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                            lineNumber: 573,\r\n                                                            columnNumber: 25\r\n                                                        }, void 0)\r\n                                                    ]\r\n                                                }, void 0, true),\r\n                                                disabled: loading,\r\n                                                onClick: handleSubmit,\r\n                                                children: (_props_fields23 = props.fields) === null || _props_fields23 === void 0 ? void 0 : (_props_fields_SubmitButtonText = _props_fields23.SubmitButtonText) === null || _props_fields_SubmitButtonText === void 0 ? void 0 : _props_fields_SubmitButtonText.value\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 560,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        }, void 0, false, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                            lineNumber: 559,\r\n                                            columnNumber: 15\r\n                                        }, undefined),\r\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            className: \"flex \",\r\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_Elements_Button_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\r\n                                                className: \"w-full sm:w-[160px] px-6 m-0 border-none \",\r\n                                                variant: \"secondary\",\r\n                                                disabled: loading,\r\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\r\n                                                    icon: _fortawesome_pro_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faCircleMinus\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                    lineNumber: 588,\r\n                                                    columnNumber: 25\r\n                                                }, void 0),\r\n                                                onClick: handleCancel,\r\n                                                children: (_props_fields24 = props.fields) === null || _props_fields24 === void 0 ? void 0 : (_props_fields_CancelButtonText = _props_fields24.CancelButtonText) === null || _props_fields_CancelButtonText === void 0 ? void 0 : _props_fields_CancelButtonText.value\r\n                                            }, void 0, false, {\r\n                                                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                                lineNumber: 584,\r\n                                                columnNumber: 17\r\n                                            }, undefined)\r\n                                        }, void 0, false, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                            lineNumber: 583,\r\n                                            columnNumber: 15\r\n                                        }, undefined)\r\n                                    ]\r\n                                }, void 0, true, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                                    lineNumber: 558,\r\n                                    columnNumber: 13\r\n                                }, undefined)\r\n                            ]\r\n                        }, void 0, true, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                            lineNumber: 427,\r\n                            columnNumber: 11\r\n                        }, undefined)\r\n                    ]\r\n                }, void 0, true, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n                    lineNumber: 287,\r\n                    columnNumber: 9\r\n                }, undefined)\r\n            ]\r\n        }, void 0, true, {\r\n            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n            lineNumber: 271,\r\n            columnNumber: 7\r\n        }, undefined)\r\n    }, void 0, false, {\r\n        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\COA\\\\logininfo\\\\Logininformation.tsx\",\r\n        lineNumber: 270,\r\n        columnNumber: 5\r\n    }, undefined);\r\n};\r\n_s(CombinedComponent, \"gPucE/eSCUim/Q4Z9INrS8xmU5A=\", false, function() {\r\n    return [\r\n        _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.useSitecoreContext,\r\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\r\n        src_stores_store__WEBPACK_IMPORTED_MODULE_6__.useAppSelector,\r\n        _mantine_form__WEBPACK_IMPORTED_MODULE_10__.useForm\r\n    ];\r\n});\r\n_c = CombinedComponent;\r\n\r\nconst Component = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_9__.withDatasourceCheck)()(CombinedComponent);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(Component, Component.name));\r\nvar _c, _c1;\r\n$RefreshReg$(_c, \"CombinedComponent\");\r\n$RefreshReg$(_c1, \"%default%\");\r\n\r\n\r\n;\r\n    // Wrapped in an IIFE to avoid polluting the global scope\r\n    ;\r\n    (function () {\r\n        var _a, _b;\r\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n        // to extract CSS. For backwards compatibility, we need to check we're in a\r\n        // browser context before continuing.\r\n        if (typeof self !== 'undefined' &&\r\n            // AMP / No-JS mode does not inject these helpers:\r\n            '$RefreshHelpers$' in self) {\r\n            // @ts-ignore __webpack_module__ is global\r\n            var currentExports = module.exports;\r\n            // @ts-ignore __webpack_module__ is global\r\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n            // This cannot happen in MainTemplate because the exports mismatch between\r\n            // templating and execution.\r\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n            // A module can be accepted automatically based on its exports, e.g. when\r\n            // it is a Refresh Boundary.\r\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n                // Save the previous exports signature on update so we can compare the boundary\r\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n                module.hot.dispose(function (data) {\r\n                    data.prevSignature =\r\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n                });\r\n                // Unconditionally accept an update to this module, we'll check if it's\r\n                // still a Refresh Boundary later.\r\n                // @ts-ignore importMeta is replaced in the loader\r\n                module.hot.accept();\r\n                // This field is set when the previous version of this module was a\r\n                // Refresh Boundary, letting us know we need to check for invalidation or\r\n                // enqueue an update.\r\n                if (prevSignature !== null) {\r\n                    // A boundary can become ineligible if its exports are incompatible\r\n                    // with the previous exports.\r\n                    //\r\n                    // For example, if you add/remove/change exports, we'll want to\r\n                    // re-execute the importing modules, and force those components to\r\n                    // re-render. Similarly, if you convert a class component to a\r\n                    // function, we want to invalidate the boundary.\r\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                        module.hot.invalidate();\r\n                    }\r\n                    else {\r\n                        self.$RefreshHelpers$.scheduleUpdate();\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                // Since we just executed the code for the module, it's possible that the\r\n                // new exports made it ineligible for being a boundary.\r\n                // We only care about the case when we were _previously_ a boundary,\r\n                // because we already accepted this update (accidental side effect).\r\n                var isNoLongerABoundary = prevSignature !== null;\r\n                if (isNoLongerABoundary) {\r\n                    module.hot.invalidate();\r\n                }\r\n            }\r\n        }\r\n    })();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/COA/logininfo/Logininformation.tsx\n"));

/***/ })

});