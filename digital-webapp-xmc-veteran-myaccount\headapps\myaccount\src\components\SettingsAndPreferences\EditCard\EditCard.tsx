import { faCircle<PERSON><PERSON>, faPen, faTrash } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Checkbox, Loader, TextInput, UnstyledButton } from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import { Field, LinkField, RichText, useSitecoreContext } from '@sitecore-jss/sitecore-jss-nextjs';
import { useMutation } from '@tanstack/react-query';
import QuestionCircle from 'assets/icons/QuestionCircle';
import axios, { AxiosResponse } from 'axios-1.4';
import Button from 'components/Elements/Button/Button';
import dayjs from 'dayjs';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { usePaymentDeletedModal } from 'src/hooks/modalhooks';
import {
  EditCardBody,
  EditCardResponse,
  RecurringPaymentResponse,
} from 'src/services/MyAccountAPI/types';
import { updateEditCardExpiry } from 'src/stores/paymentSlice';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { getCardIcon } from 'src/utils/cardHelper';
import { z } from 'zod';
import Tooltip from 'components/Elements/Tooltip/Tooltip';
import PageBuilder from 'components/common/PageBuilder/PageBuilder';

type EditCardProps = ComponentProps & {
  fields: {
    data: {
      item: {
        AddEditCreditOrDebitCardText: Field<string>;
        CardEndingText: Field<string>;
        AccountEndingText: Field<string>;
        CardDetailsText: Field<string>;
        CardNumberText: Field<string>;
        NameOnCardText: Field<string>;
        ExpDateText: Field<string>;
        SecurityCodeText: Field<string>;
        ZipcodeText: Field<string>;
        SavePaymentInfoText: Field<string>;
        NicknameText: Field<string>;
        CardDefaultPaymentSavetext: Field<string>;
        BackButton: Field<string>;
        BackButtonLink?: { jsonValue: LinkField };
        DeletePaymentMethodBtnText: Field<string>;
        CancelChangesText: Field<string>;
        SaveChangesText: Field<string>;
        ContinueButtonText: Field<string>;
        ScheduledPaymentErrorText: Field<string>;
        AutoPayErrorText: Field<string>;
        EditTextForApp: Field<string>;
        SecurityCodeToolTip: Field<string>;
        DeleteTitle: Field<string>;
        DeleteDescription: Field<string>;
        DeleteItem: Field<string>;
        ConfirmText: Field<string>;
        CancelText: Field<string>;
        ExpirationDateErrorMessage: Field<string>;
        InvalidNickNameErrorMessage: Field<string>;
        ValidDateErrorMessage: Field<string>;
        SecurityCodeErrorMessage: Field<string>;
      };
    };
  };
};

const EditCard = (props: EditCardProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  if (isPageEditing) return <PageBuilder componentName="EditCard" />;

  const [showErrorMessage, setShowErrorMessage] = useState<string | undefined>('');
  const [isEdit, setIsEdit] = useState(false);
  const [isDisabled, setIsDisabled] = useState(true);
  const dispatch = useAppDispatch();
  let editCardDetails = undefined;
  let contractAccount: string | undefined = undefined;
  let partner = undefined;
  let selectedAccount: string | undefined = undefined;
  if (!isPageEditing) {
    editCardDetails = useAppSelector((state) => state?.payment?.editCardDetails);
    contractAccount = useAppSelector(
      (state) => state?.authuser?.accountSelection?.contractAccount?.value
    );
    partner = useAppSelector((state) => state?.authuser?.bpNumber);
    selectedAccount = useAppSelector(
      (state) => state?.authuser?.accountSelection?.contractAccount?.value
    );
  }

  const securityCodeSchema = (cardBrand: string, errorMsg: string) =>
    z.string().refine(
      (val) => {
        if (cardBrand.toLowerCase() === 'amex' || cardBrand.toLowerCase() === 'americanexpress') {
          return /^\d{4}$/.test(val);
        } else {
          return /^\d{3}$/.test(val);
        }
      },
      {
        message: errorMsg,
      }
    );

  const EditCardSchema = z.object({
    expiration: z
      .string()
      .refine((val) => dayjs(val, 'MM/YY', true).isValid(), {
        message: props.fields?.data.item.ValidDateErrorMessage?.value,
      })
      .refine(
        (val) => {
          const expirationDate = dayjs(val, 'MM/YY', true);
          return expirationDate.isAfter(dayjs(), 'month');
        },
        { message: props.fields?.data.item.ExpirationDateErrorMessage?.value }
      ),
    // securityCode: z
    //   .string()
    //   .min(3, { message: props.fields?.data.item.SecurityCodeErrorMessage?.value })
    //   .max(4, { message: props.fields?.data.item.SecurityCodeErrorMessage?.value }),
    securityCode: securityCodeSchema(
      editCardDetails?.cardBrand ?? '',
      props.fields?.data.item.SecurityCodeErrorMessage?.value
    ),
    nickName: z
      .string()
      .nonempty({ message: props.fields?.data.item.InvalidNickNameErrorMessage?.value }),
  });

  type EditCardFormType = z.infer<typeof EditCardSchema>;

  const router = useRouter();

  const [hasRecurringPayment, setHasRecurringPayments] = useState(false);
  const form = useForm<EditCardFormType>({
    initialValues: {
      expiration: dayjs(editCardDetails?.expiration).format('MM/YY'),
      securityCode: '',
      nickName: editCardDetails?.nickname,
    },
    validate: zodResolver(EditCardSchema),
    validateInputOnBlur: true,
    validateInputOnChange: true,
  });

  useEffect(() => {
    if (isEdit) setShowErrorMessage('');
  }, [isEdit]);

  useEffect(() => {
    async function fetchRecurringAccounts() {
      const recurringAccount = await axios.get<RecurringPaymentResponse>(
        '/api/autopay/getRecurringAutoPay',
        {
          params: {
            accountNumber: selectedAccount,
          },
        }
      );
      if (
        recurringAccount &&
        recurringAccount?.data &&
        recurringAccount?.data?.result &&
        recurringAccount?.data?.result?.profileId == editCardDetails?.profileId
      ) {
        setHasRecurringPayments(true);
      } else {
        setHasRecurringPayments(false);
      }
      setIsDisabled(false);
    }
    if (selectedAccount) fetchRecurringAccounts();
  }, [selectedAccount]);

  const { openPayementDeletedModal } = usePaymentDeletedModal();

  const deletecard = async () => {
    setShowErrorMessage('');
    if (editCardDetails?.hasSchedulePayments) {
      setShowErrorMessage(props?.fields?.data.item.ScheduledPaymentErrorText?.value);
    } else if (editCardDetails?.hasRecurringPayments) {
      setShowErrorMessage(props?.fields?.data.item.AutoPayErrorText?.value);
    } else {
      openPayementDeletedModal(
        editCardDetails?.nickname,
        editCardDetails?.cardDisplayNumber,
        true,
        editCardDetails?.accountId,
        null,
        editCardDetails?.profileId,
        props?.fields?.data.item.DeleteTitle?.value,
        props?.fields?.data.item.DeleteDescription?.value,
        props?.fields?.data.item.DeleteItem?.value,
        props?.fields?.data.item.ConfirmText?.value,
        props?.fields?.data.item.CancelText?.value
      );
    }
  };
  const mutation = useMutation({
    mutationFn: (values: EditCardFormType) => {
      return axios.post<
        EditCardResponse,
        AxiosResponse<EditCardResponse, EditCardBody>,
        EditCardBody
      >('/api/myaccount/payments/editcard', {
        ExpiryDate: dayjs(values.expiration, 'MM/YY')
          .endOf('month')
          .startOf('day')
          .format()
          .toString(),
        CVV: values.securityCode,
        ContractAccount: contractAccount,
        Partner: partner,
        CardHolder: editCardDetails?.cardholderName,
        CardDescription: values.nickName,
        CardToken: editCardDetails?.accountId,
        CardType: 0,
      });
    },
    onSuccess: (data) => {
      if (!data.data.hasErrors) {
        form.values.securityCode = '';
        if (!isPageEditing) {
          dispatch(
            updateEditCardExpiry({
              expiration: dayjs(form.values.expiration, 'MM/YY')
                .endOf('month')
                .startOf('day')
                .format()
                .toString(),
              nickName: form.values.nickName,
            })
          );
        }
        setIsEdit(false);
      }
    },
  });

  if (mutation.isLoading)
    return (
      <div className="w-full lg:w-[592px] sm:ml-[420px] ipad:ml-0 shadow-3xl p-5 rounded-xl flex flex-row items-center justify-center">
        <Loader />
      </div>
    );

  return (
    <form className="w-full sm:max-w-[692px] pl-0 mt-6 sm:ml-[420px] ipad:ml-0">
      <div className="flex flex-col w-full items-center gap-5 lg:items-start lg:gap-8 px-4 sm:px-0">
        <p className="font-primaryBlack text-textUndenary  text-plus2 lg:text-plus4">
          {props.fields.data.item.AddEditCreditOrDebitCardText.value}
        </p>
        <div className="flex flex-row w-full items-center gap-3 pt-10 border-t border-borderPrimary">
          <FontAwesomeIcon
            icon={getCardIcon(editCardDetails?.accountId)}
            className="w-[48px] h-[32px] text-textPrimary "
          />
          <div className="flex flex-col flex-grow lg:flex-row lg:gap-8">
            <p className="text-textUndenary  font-primaryBlack text-minus2 lg:text-base">
              {editCardDetails?.nickname}
            </p>
            <p className="text-textQuattuordenary font-primaryRegular text-minus3 lg:text-minus2 hidden">
              {props.fields?.data.item.CardEndingText?.value} {editCardDetails?.cardDisplayNumber}
            </p>
          </div>
          {!isEdit && (
            <UnstyledButton
              type="button"
              className="flex flex-row text-textUndenary  hover:text-textSecondary font-primaryBlack items-center gap-1 text-minus2 sm:text-minus1 justify-center"
              onClick={() => setIsEdit(true)}
            >
              <span className="hidden">{props.fields?.data?.item?.EditTextForApp.value}</span>
              <FontAwesomeIcon icon={faPen} className="lg:translate-y-[-1px]" />
            </UnstyledButton>
          )}
        </div>
        <div className="w-full flex flex-col gap-5 text-minus2 lg:text-base p-0 shadow-none rounded-none pb-10 border-b border-borderPrimary">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2 sm:basis-2/4">
              <p className="text-textUndenary  font-primaryBlack">
                {props.fields.data.item.NameOnCardText.value}
              </p>
              <p className="text-textQuattuordenary font-primaryRegular">
                {editCardDetails?.cardholderName}
              </p>
            </div>
            <div className="flex flex-col gap-2 text-minus2 sm:text-base sm:basis-2/4">
              <p className="text-textUndenary  font-primaryBlack">
                {props.fields.data.item.CardNumberText.value}
              </p>
              <p className=" text-textQuattuordenary font-primaryRegular">
                {props.fields?.data.item.CardEndingText?.value} {editCardDetails?.cardDisplayNumber}
              </p>
            </div>
          </div>
          {isEdit ? (
            <>
              <div className="flex flex-col gap-4">
                <div className="flex flex-col gap-2 text-minus2 sm:text-base sm:basis-2/4">
                  <p className="text-textQuattuordenary font-primaryRegular">
                    {props.fields.data.item.ExpDateText.value}
                  </p>
                  <TextInput
                    styles={{
                      error: {
                        fontSize: '12px',
                      },
                    }}
                    className="max-w-[200px]"
                    {...form.getInputProps('expiration')}
                  />
                </div>
                <div className="flex flex-col gap-2 sm:basis-2/4">
                  <p className="text-textQuattuordenary font-primaryRegular">
                    {props.fields.data.item.SecurityCodeText.value}
                    <Tooltip
                      content={
                        props.fields?.data.item.SecurityCodeToolTip.value != ''
                          ? props.fields?.data.item.SecurityCodeToolTip
                          : { value: '' }
                      }
                      className="selected-tooltip"
                      arrowclassName="selected-tooltip-icon"
                    >
                      <QuestionCircle />
                    </Tooltip>
                  </p>
                  <TextInput
                    styles={{
                      error: {
                        fontSize: '12px',
                      },
                    }}
                    className="max-w-[200px]"
                    {...form.getInputProps('securityCode')}
                  />
                </div>
              </div>
              <div className="flex flex-col  gap-2 sm:basis-2/4">
                <p className="text-textQuattuordenary font-primaryRegular">
                  {props.fields.data.item.NicknameText.value}
                </p>
                <TextInput
                  styles={{
                    error: {
                      fontSize: '12px',
                    },
                  }}
                  className="max-w-[200px]"
                  {...form.getInputProps('nickName')}
                />
              </div>
            </>
          ) : (
            <>
              <div className="flex flex-col gap-4">
                <div className="flex flex-col gap-2 sm:basis-2/4">
                  <p className="text-textUndenary  font-primaryBlack">
                    {props.fields.data.item.ExpDateText.value}
                  </p>
                  <p className=" text-textQuattuordenary font-primaryRegular">
                    {dayjs(editCardDetails?.expiration).format('MM/YY')}
                  </p>
                </div>
                <div className="flex flex-col gap-2 sm:basis-2/4">
                  <p className="text-textUndenary  font-primaryBlack">
                    {props.fields.data.item.SecurityCodeText.value}
                  </p>
                  <p className="text-textQuattuordenary font-primaryRegular">***</p>
                </div>
              </div>
              <div className="flex flex-col gap-2 text-minus2 sm:text-base  sm:basis-2/4">
                <p className="text-textUndenary  font-primaryBlack">
                  {props.fields.data.item.NicknameText.value}
                </p>
                <p className="text-textQuattuordenary font-primaryRegular">
                  {editCardDetails?.nickname}
                </p>
              </div>
            </>
          )}
          <div className="flex flex-col gap-5 lg:flex-row ">
            {/* <div className="flex flex-col gap-2 text-minus2 sm:text-base sm:basis-2/4">
              <p className="text-txublue font-primaryBlack">
                {props.fields.NicknameText.value}
              </p>
              <p className=" text-textQuattuordenary font-primaryRegular">
                {editCardDetails.nickname}
              </p>
            </div> */}
            {/* <div className="flex flex-col gap-2 text-minus2 sm:text-base sm:basis-2/4">
              <p className="text-textUndenary  font-primaryBlack  ">
                {props.fields.ZipcodeText.value}
              </p>
              <p className="text-textQuattuordenary font-primaryRegular">
                {editCardDetails.zipCode}
              </p>
            </div> */}
          </div>
          {isEdit && (
            <Checkbox
              radius="xs"
              label={props.fields.data.item.CardDefaultPaymentSavetext.value}
              className="hidden"
            />
          )}
        </div>
      </div>
      <div className="px-8 sm:px-0">
        {isEdit ? (
          <div className="flex w-full flex-col items-center lg:flex-row gap-6 sm:gap-4 mt-8 mb-12">
            <div>
              <Button
                type="button"
                onClick={() => {
                  form.validate();
                  if (form.isValid()) {
                    mutation.mutate(form.values);
                  }
                }}
                className="w-full lg:w-auto"
              >
                {props.fields.data.item.SaveChangesText.value}
              </Button>
            </div>

            <div>
              <Button
                onClick={() => {
                  form.reset();
                  setIsEdit(false);
                }}
                type="button"
                variant="secondary"
                className="flex w-full lg:w-auto"
              >
                {props.fields.data.item.CancelChangesText.value}
              </Button>
              <UnstyledButton
                type="button"
                className="hidden py-0"
                onClick={() => setIsEdit(false)}
              >
                {props.fields.data.item.CancelChangesText.value}
                <FontAwesomeIcon className="tee:hidden" icon={faCircleMinus} />
              </UnstyledButton>
            </div>
          </div>
        ) : (
          <div className="w-full">
            <RichText
              className="font-primaryBold mt-3 text-minus1 text-textDenary"
              field={{ value: showErrorMessage }}
            />

            <div className="flex flex-col sm:flex-row gap-6 w-full lg:flex-row-reverse sm:justify-between items-center mt-5  mb-14">
              <div>
                <UnstyledButton
                  type="button"
                  className="flex flex-row items-center gap-2 py-0 font-primaryBlack text-textDenary text-minus1 disabled:opacity-50"
                  onClick={deletecard}
                >
                  {props.fields.data.item.DeletePaymentMethodBtnText.value}{' '}
                  <FontAwesomeIcon icon={faTrash} />
                </UnstyledButton>
              </div>
              <div>
                <Button
                  showLoader={true}
                  type="button"
                  className="w-full h-[56px] lg:w-fit"
                  variant="secondary"
                  onClick={() =>
                    router.push(
                      props.fields?.data.item.BackButtonLink?.jsonValue.value?.href as string
                    )
                  }
                >
                  {props.fields.data.item.BackButton.value}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </form>
  );
};

export { EditCard };
const Component = EditCard;
export default aiLogger(Component, Component.name);
