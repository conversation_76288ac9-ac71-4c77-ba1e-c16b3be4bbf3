import { faChevronDoubleRight } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Text,
  Field,
  withDatasourceCheck,
  LinkField,
  Link,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios-1.4';
import Loader from 'components/common/Loader/Loader';
import PageBuilder from 'components/common/PageBuilder/PageBuilder';
import dayjs from 'dayjs';
import { logToAppInsights } from 'lib/app-insights-log-error';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useLoader } from 'src/hooks/modalhooks';
import { UsageOverviewResponse } from 'src/services/UsageOverviewAPI/types';
import { useAppSelector } from 'src/stores/store';

type ForecastedBillProps = ComponentProps & {
  fields: {
    Title: Field<string>;
    ErrorText: Field<string>;
    DateFormat: Field<string>;
    ViewDetailedUsageLabel: Field<string>;
    ViewDetailedUsageLink: LinkField;
    ForecatedBillAmount: Field<string>;
    ForecastedUsage: Field<string>;
    HideforBusinessUser: Field<boolean>;
    ErrorTitle: Field<string>;
  };
};

const ForecastedBill = (props: ForecastedBillProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;

  if (isPageEditing) return <PageBuilder componentName="ForecastedBill" />;

  const branding = process.env.NEXT_PUBLIC_BRAND || 'site';
  const { openModal } = useLoader();
  let selectedEsiid = undefined;
  let isBusinessUser = undefined;
  if (!isPageEditing) {
    selectedEsiid = useAppSelector((state) => state?.authuser?.accountSelection.esiid?.value);

    isBusinessUser = useAppSelector((state) => state?.authuser?.isBusinessUser);
  }
  const isDisabledForBsUser = isBusinessUser && props.fields.HideforBusinessUser?.value === true;

  const numberFormatter = new Intl.NumberFormat('en-US');

  const { isLoading, data, error } = useQuery({
    queryKey: ['usageoverview', selectedEsiid],
    queryFn: () =>
      axios
        .get<UsageOverviewResponse>('/api/usageoverview', {
          params: {
            esiID: selectedEsiid,
          },
        })
        .then((res) => {
          logToAppInsights(
            `${branding}-login-myaccount summary-clientid`,
            `Received the usageoverview data for the esiid ${selectedEsiid}`
          );
          return res.data;
        })
        .catch((ex) => {
          logToAppInsights(
            `${branding}-login-myaccount summary-clientid`,
            `Failed to receive the usageoverview data for the esiid ${selectedEsiid}. Exception: ${ex}`
          );
        }),
    enabled: !!selectedEsiid && !isDisabledForBsUser,
  });

  if (isDisabledForBsUser) return <></>;

  if (isLoading)
    return (
      <div className="w-full h-64">
        <Loader />
      </div>
    );

  if (error || data?.hasErrors)
    return (
      <div className="p-6 rounded-xl sm:p-0 w-full md:w-full">
        <div className="text-center text-plus2 text-textUndenary font-primaryBlack">
          <Text field={props.fields.ErrorTitle} />
        </div>
        <div className="items-center justify-center flex min-h-[135px] text-start text-textUndenary sm:text-minus1 font-primaryRegular bg-bgOctodenary mt-4 p-2 border-2 border-borderQuattuordenary">
          {props.fields.ErrorText.value}
        </div>
      </div>
    );

  if (data && data?.result && data?.result?.length > 0) {
    return (
      <>
        {data?.result[0]?.suppressionReason.trim().length <= 0 && (
          <div className="flex flex-col gap-4">
            <div className="text-plus2 font-primaryBold text-textUndenary">
              {props.fields.Title?.value.replace(
                '{Date}',
                dayjs(data?.result[0].asOfDate.replace(/T00:00:00Z/g, '')).format(
                  props?.fields?.DateFormat?.value?.length > 0
                    ? props?.fields?.DateFormat?.value
                    : 'MM/DD/YYYY'
                )
              )}
            </div>
            <div className="text-plus4 text-textPrimary font-primaryRegular">
              {props.fields.ForecatedBillAmount.value
                .replace('{BillAmountLowerLevel}', data?.result[0].billAmountLowerLevel.toString())
                .replace('{BillAmountUpperLevel}', data?.result[0].billAmountUpperLevel.toString())}
            </div>
            <div className="text-plus1 text-textPrimary font-primaryMedium">
              {props.fields.ForecastedUsage.value
                .replace(
                  '{UsageLowerLevel}',
                  numberFormatter.format(data?.result[0].usageLowerLevel).toString()
                )
                .replace(
                  '{UsageUpperLevel}',
                  numberFormatter.format(data?.result[0].usageUpperLevel).toString()
                )}
            </div>
            <div className="mt-3 text-minus3 font-primaryMedium text-textPrimary hover:text-textSecondary no-underline underline-offset-4 decoration-2 decoration-textPrimary hover:decoration-hoverPrimary cursor-pointer">
              <Link
                className="px-[40px] py-[14px] h-[50px] text-base font-primaryBold rounded-[6px] border-2 border-borderSecondary text-textSecondary hover:border-borderPrimary focus:ring-1 focus:ring-bgVigintioctonary disabled:opacity-50 hover:bg-bgPrimary hover:text-textQuinary"
                field={{
                  value: {
                    href: props.fields.ViewDetailedUsageLink.value.href,
                  },
                }}
                onClick={() => openModal()}
              >
                {props.fields.ViewDetailedUsageLabel.value}
                <FontAwesomeIcon
                  className="text-minus4 relative top-[1px] hidden pl-2"
                  icon={faChevronDoubleRight}
                />
              </Link>
            </div>
          </div>
        )}
        {data?.result[0]?.suppressionReason.trim().length > 0 && (
          <div className="p-6 rounded-xl sm:p-0 w-full md:w-full">
            <div className="text-center text-plus2 text-textUndenary font-primaryBlack">
              <Text field={props.fields.ErrorTitle} />
            </div>
            <div className="text-center flex items-center justify-center min-h-[135px] text-textUndenary sm:text-minus1 font-primaryRegular bg-bgOctodenary mt-4 p-2 border-2 border-borderQuattuordenary">
              {data?.result[0].suppressionReason}
            </div>
          </div>
        )}
      </>
    );
  } else {
    return (
      <div className=" p-6 rounded-xl sm:p-0 w-full md:w-full">
        <div className="text-center text-plus2 text-textUndenary font-primaryBlack">
          <Text field={props.fields.ErrorTitle} />
        </div>
        <div className="text-center flex items-center justify-center min-h-[135px] text-textUndenary sm:text-minus1 font-primaryRegular bg-bgOctodenary mt-4 p-2 border-2 border-borderQuattuordenary">
          {props.fields.ErrorText.value}
        </div>
      </div>
    );
  }
};
export { ForecastedBill };
const Component = withDatasourceCheck()<ForecastedBillProps>(ForecastedBill);
export default aiLogger(Component, Component.name);
