import { useEffect } from 'react';
import { deleteCookie } from 'cookies-next';
import { useRouter } from 'next/router';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import * as constants from 'src/utils/constants';
import { LinkField, useSitecoreContext } from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import { useDispatch } from 'react-redux';
import { clearSwapOrRenewalStatus, clearsAuthSlice } from 'src/stores/authUserSlice';
import { clearsPlanSlice } from 'src/stores/planSlice';
import { clearsHeaderSlice } from 'src/stores/headerSlice';
import { clearsPaymentSlice } from 'src/stores/paymentSlice';
import { useLoader } from 'src/hooks/modalhooks';
import PageBuilder from 'components/common/PageBuilder/PageBuilder';

type LogoutProps = ComponentProps & {
  fields: {
    LogoutUrl: LinkField;
  };
};

const Logout = (props: LogoutProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;

  if (isPageEditing) return <PageBuilder componentName="Logout" />;

  const { openModal } = useLoader();
  const router = useRouter();
  const dispatch = useDispatch();
  useEffect(() => {
    openModal();
    dispatch(clearSwapOrRenewalStatus());
    dispatch(clearsAuthSlice());
    dispatch(clearsPlanSlice());
    dispatch(clearsHeaderSlice());
    dispatch(clearsPaymentSlice());
    clearCookies();
    localStorage.clear();
    sessionStorage.clear();
    const reditrectUrl = props?.fields?.LogoutUrl?.value?.href
      ? props?.fields?.LogoutUrl?.value?.href
      : '/login';
    router.push(reditrectUrl as string);
  });

  async function clearCookies() {
    const cookieDomain =
      process.env.NODE_ENV === 'development' ? '' : process.env.NEXT_PUBLIC_COOKIES_OPTIONS_DOMAIN;
    constants.COOKIES_LIST.map((cookie_name) => {
      deleteCookie(cookie_name, {
        path: '/',
        sameSite: 'none',
        secure: true,
        domain: cookieDomain,
      });
    });
  }

  return <></>;
};
export { Logout };
export default aiLogger(Logout, Logout.name);
