import {
  faArrowRight,
  faChevronsRight,
  faSpinner,
  faEye,
  faEyeSlash,
  faChevronDown,
  faChevronUp,
} from '@fortawesome/pro-light-svg-icons';
import {
  Text,
  Field,
  withDatasourceCheck,
  RichText,
  LinkField,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { TextInput, Checkbox, Select as MantineSelect } from '@mantine/core';
import { useForm } from '@mantine/form'; // Import useForm from Mantine
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import Button from 'components/Elements/Button/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useAppSelector, useAppDispatch } from 'src/stores/store';
import {
  clearAccountInfo,
  setSecurityQuestion,
  setSecurityAnswer,
  setAccountInfo,
} from 'src/stores/coaSlice';
import axios, { AxiosError } from 'axios';
import Tooltip from 'components/Elements/Tooltip/Tooltip';
import QuestionCircle from 'assets/icons/QuestionCircle';
import { faCircleMinus } from '@fortawesome/pro-regular-svg-icons';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';

export interface SecurityQuestionTypes {
  fields: {
    Title: Field<string>;
    Text: Field<string>;
  };
}
type CombinedProps = ComponentProps & {
  fields: {
    VerifyYourInfoText: Field<string>;
    VerifyYourInfoDescriptionText: Field<string>;
    EmailAddressText: Field<string>;
    PasswordText: Field<string>;
    ConfirmPasswordText: Field<string>;
    SecurityQuestionText: Field<string>;
    SpecifyAnswerText: Field<string>;
    LoginInfoText: Field<string>;
    YesCheckBoxText: Field<string>;
    IDontCheckBoxText: Field<string>;
    AcceptedCheckBoxText: Field<string>;
    SubmitButtonText: Field<string>;
    CancelButtonText: Field<string>;
    PasswordCharacterLimitText: Field<string>;
    SelectQuestionText: Field<string>;
    AnswerValidationText: Field<string>;
    SecurityQuestionValidationText: Field<string>;
    PasswordCharactersValidationText: Field<string>;
    PasswordMatchValidationText: Field<string>;
    InvalidEmailValidationText: Field<string>;
    PasswordNumerical: Field<string>;
    PasswordNotUsername: Field<string>;
    PasswordsDoNotMatch: Field<string>;
    SaveAllFields: Field<string>;
    AgreeTC: Field<string>;
    SecurityQuestions: SecurityQuestionTypes[];
    problemCreatingAccount: Field<string>;
    accountinformationurl: LinkField;
    verifyinformationurl: LinkField;
    logininformationurl: LinkField;
    TermsConditionsURL: Field<string>;
    TermsConditions: Field<string>;
    GenericeErrorMessage: Field<string>;
    LoginEmailAlreadyExist: Field<string>;
    EmailRequirementsText: Field<string>;
    rxUsername: Field<string>;
    UsernameText: Field<string>;
    WhyPaperlessBillingTitleText: Field<string>;
    BenefitsofPaperlessText: Field<string>;
    PaperlessBillingDropDownText: Field<string>;
    BillingConsentToolTipText: Field<string>;
    SelectSecurityQuestionText: Field<string>;
    rxPassword: Field<string>;
  };
};

const CombinedComponent = (props: CombinedProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  const [loading, setLoading] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [isAcceptedChecked, setIsAcceptedChecked] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showSecurityAnswer, setShowSecurityAnswer] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isYesCheckedForSpecialOffer, setIsYesCheckedForSpecialOffer] = useState(false);
  const router = useRouter();
  let accountInfo = undefined;
  let dispatch: ReturnType<typeof useAppDispatch>;
  if (!isPageEditing) {
    accountInfo = useAppSelector((state) => state?.coa?.accountInfo);
    dispatch = useAppDispatch();
  }

  const termsUrl = props.fields?.TermsConditionsURL?.value || '#';
  const acceptText = props.fields?.AcceptedCheckBoxText?.value || 'I accept the';
  const termsText = props.fields?.TermsConditions?.value || 'Terms and Conditions';
  const regexUsername = props.fields?.rxUsername?.value?.slice(1, -1) || '';
  const [isPaperlessChecked, setIsPaperlessChecked] = useState(
    accountInfo?.isCommunicationMethodEmail === true ? true : false
  );
  const showToolTip = props.fields?.BillingConsentToolTipText?.value?.trim().length > 0;
  const storedEmail = useAppSelector((state) => state?.coa?.accountInfo?.email);
  const [isUsernameDisabled, setIsUsernameDisabled] = useState(false);

  // Initialize the form with useForm
  const form = useForm({
    initialValues: {
      username: '',
      password: '',
      confirmPassword: '',
      securityQuestion: '',
      securityAnswer: '',
    },
    validate: {
      username: (value) => {
        const isValidUserName = new RegExp(regexUsername).test(value);
        return isValidUserName ? null : props.fields?.InvalidEmailValidationText?.value;
      },
      password: (value) => {
        const regexPattern = props?.fields?.rxPassword?.value;
        const regex = new RegExp(regexPattern).test(value);
        const isValidPassword =
          regex || /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^]).{12,}$/.test(value);
        return isValidPassword ? null : props.fields?.PasswordCharactersValidationText?.value;
      },
      confirmPassword: (value, values) => {
        const doPasswordsMatch = value === values.password;
        return doPasswordsMatch ? null : props.fields?.PasswordsDoNotMatch?.value;
      },
      securityQuestion: (value) => {
        const isQuestionSelected = !!value;
        return isQuestionSelected ? null : props.fields?.SelectQuestionText?.value;
      },
      securityAnswer: (value) => {
        const isAnswerValid = value.length >= 3;
        return isAnswerValid ? null : props.fields?.AnswerValidationText?.value;
      },
    },
    validateInputOnBlur: true,
  });

  useEffect(() => {
    const fetchUserByEmail = async () => {
      if (!storedEmail) return;
      try {
        const response = await axios.get(
          `/api/forgotpassword/checkusername?username=${storedEmail}`
        );
        const apiResult = response?.data?.result;
        form.setFieldValue('username', !apiResult ? storedEmail : '');
        setIsUsernameDisabled(!apiResult);
      } catch (error) {
        const err = error as AxiosError;
        logErrorToAppInsights(err, { componentStack: 'Login' });
        console.error('Error fetching user by email:', err.response?.data);
      }
    };
    fetchUserByEmail();
  }, []);

  const handleSubmit = async () => {
    if (!isAcceptedChecked) {
      setErrorMessage(props.fields?.AgreeTC?.value);
      return;
    }
    if (!form.validate().hasErrors) {
      setErrorMessage('');
      setLoading(true);

      const requestBody = {
        Username: form.values.username,
        Email: accountInfo?.email,
        SecurityQuestion: form.values.securityQuestion,
        SecurityAnswer: form.values.securityAnswer,
        CustomerClassification: accountInfo?.customerClassification,
        EnrollmentChannel: 1,
        FirstName: accountInfo?.firstName || '',
        LastName: accountInfo?.lastName || '',
        LanguagePreference: 'English',
        OrganizationBPNumber: accountInfo?.organizationBpNumber ?? null,
        Password: form.values.password,
        PaperlessBilling: isPaperlessChecked,
        SpecialOffers: isYesCheckedForSpecialOffer,
        PersonBPNumber: accountInfo?.partnerNumber || '',
        SendEmailInvitation: true,
        isBusinessUser:
          accountInfo && accountInfo.customerClassification.toString() == '3' ? false : true,
        AccountNumber: accountInfo?.accountNumber || '',
        Portal: accountInfo && accountInfo.customerClassification.toString() == '3' ? 4 : 5, // as discussed with Pranay adding 4 for reidential and 5 for business
        isEnrollment: true,
      };

      try {
        setLoading(true);
        const response = await axios.post('/api/createonlineaccount/create', requestBody);
        if (response.data.result) {
          if (!isPageEditing) {
            dispatch(
              setAccountInfo({
                ...accountInfo,
                email: form.values.username,
                accountNumber: accountInfo?.accountNumber || '',
                firstName: accountInfo?.firstName || '',
                lastName: accountInfo?.lastName || '',
                partnerNumber: accountInfo?.partnerNumber || '',
                isCommunicationMethodEmail:
                  accountInfo?.isCommunicationMethodEmail === true ? true : false,
                customerClassification: accountInfo?.customerClassification || 4,
                organizationBpNumber: accountInfo?.organizationBpNumber || '',
                isBusiness: accountInfo?.isBusiness || false,
              })
            );

            dispatch(setSecurityQuestion(form.values.securityQuestion));
            dispatch(setSecurityAnswer(form.values.securityAnswer));
          }
          router.push(props.fields.verifyinformationurl?.value.href as string); //'/coa/Verify-Login-Information');
        } else {
          setLoading(false);
          let errMsg: string;
          if (response.data?.messages[0]) {
            if (response.data?.messages[0] === 'Username Duplicate Message') {
              errMsg = props.fields?.LoginEmailAlreadyExist.value;
            } else {
              errMsg = response.data?.messages[0];
            }
            setErrorMessage(errMsg);
          } else {
            errMsg = props.fields.GenericeErrorMessage.value;
            setErrorMessage(errMsg);
          }
        }
      } catch (error) {
        setLoading(false);
        setErrorMessage(props.fields.problemCreatingAccount?.value);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleCancel = async () => {
    if (!isPageEditing) {
      dispatch(clearAccountInfo());
    }
    router.push(props.fields.accountinformationurl?.value.href as string); //'coa/account-information');
  };

  //console.log('SecurityQuestions= ' + props.fields.SecurityQuestions); // Debugging line to check the data

  return (
    <div>
      <div className="w-full sm:max-w-[1040px] my-10 m-auto text-start">
        <Text
          tag="h1"
          field={{ value: props.fields?.VerifyYourInfoText?.value }}
          className="sm:text-plus3 text-base sm:mr-auto pb-6 font-primaryBold text-textUndenary"
        />
        <Text
          className="font-primaryRegular text-textUndenary text-base"
          tag="p"
          field={{ value: 'Enter an email and password to be associated with this account.' }}
        />
        {/* <Text
        tag="p"
        field={{ value: props.fields?.LoginInfoText?.value }}
        className="font-mProBlack tee:font-gProMedium align-middle text-txublue tee:text-tee-darkblue sm:text-plus2 text-base tee:text-plus2 tee:sm:text-base sm:mr-auto pb-6"
      /> */}
        <div>
          <div className="flex flex-row w-full gap-20 justify-start mt-5">
            <div className="w-full px-4 sm:px-0 sm:max-w-[500px]">
              {/* user name */}
              <div className="flex py-2 justify-between flex-col items-start gap-2">
                <div
                  className={`w-[21rem] flex text-left items-center md:w-full  ${
                    form.errors.username ? 'mb-[30px]' : ''
                  }`}
                ></div>
                <div className="flex flex-col">
                  <TextInput
                    label={props.fields?.UsernameText?.value}
                    {...form.getInputProps('username')}
                    autoComplete="new-password"
                    disabled={isUsernameDisabled}
                    styles={{
                      input: { textAlign: 'left', width: '21rem' },
                      error: { maxWidth: '21rem', wordWrap: 'break-word' },
                    }}
                  />
                  <Text
                    tag="p"
                    field={{ value: props.fields?.EmailRequirementsText?.value }}
                    className="font-primaryRegular text-textUndenary mt-2 text-minus2 sm:text-minus1 text-start md:text-left sm:mr-auto"
                  />
                </div>
              </div>
              {/* password */}
              <div className="flex py-2 justify-between flex-col items-start gap-2">
                <div className="w-[21rem] flex items-center md:w-full"></div>
                <div className="relative">
                  <TextInput
                    label={props.fields?.PasswordText?.value}
                    {...form.getInputProps('password')}
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    styles={{
                      input: { textAlign: 'left', width: '21rem' },
                      error: { maxWidth: '21rem', wordWrap: 'break-word' },
                    }}
                  />
                  <FontAwesomeIcon
                    icon={showPassword ? faEyeSlash : faEye}
                    className="absolute right-[25px] sm:right-[85px] top-[50px] cursor-pointer text-textPrimary"
                    onClick={() => setShowPassword(!showPassword)}
                  />
                  <Text
                    tag="p"
                    field={{ value: props.fields?.PasswordCharacterLimitText?.value }}
                    className="mt-2 font-primaryRegular text-textUndenary text-start md:text-left sm:mr-auto text-minus2 sm:text-minus1"
                  />
                </div>
              </div>

              <div className="flex py-2 justify-between flex-col items-start gap-2">
                <div
                  className={`w-[21rem] flex flex-col md:w-full ${
                    form.errors.confirmPassword ? 'mb-[30px]' : ''
                  }`}
                ></div>
                <div className="relative">
                  <TextInput
                    {...form.getInputProps('confirmPassword')}
                    label={props.fields?.ConfirmPasswordText?.value}
                    type={showConfirmPassword ? 'text' : 'password'}
                    styles={{
                      input: { textAlign: 'left', width: '21rem' },
                      error: { maxWidth: '21rem', wordWrap: 'break-word' },
                    }}
                  />
                  <FontAwesomeIcon
                    icon={showConfirmPassword ? faEyeSlash : faEye}
                    className="absolute right-[16px] top-[50px] cursor-pointer text-textPrimary"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  />
                </div>
              </div>

              <div className="flex py-2 justify-between flex-col items-start gap-2">
                <div className="w-[21rem] flex items-center md:w-full flex-col">
                  <Text
                    tag="h2"
                    field={{ value: props.fields?.SecurityQuestionText?.value }}
                    className="font-primaryBold text-textUndenary text-plus3 mt-5 sm:text-[32px] text-left mr-auto w-full"
                  />
                  <Text
                    tag="p"
                    field={props.fields?.SelectSecurityQuestionText}
                    className="font-primaryRegular text-textUndenary text-base sm:text-minus1 text-left mr-auto pt-3 pb-2"
                  />
                </div>
                <MantineSelect
                  className="w-[21rem] text-[16px] text-base mt-5 font-primaryRegular text-textUndenary tracking-normal leading-[24px]"
                  placeholder="Choose a security question"
                  styles={{
                    root: {
                      width: '21rem',
                      [`@media (max-width: 767px)`]: {
                        width: '100%',
                      },
                    },
                  }}
                  {...form.getInputProps('securityQuestion')}
                  data={
                    props?.fields?.SecurityQuestions?.map(
                      (securityquestion) => securityquestion?.fields?.Title?.value
                    ) ?? []
                  }
                  rightSection={
                    <FontAwesomeIcon icon={faChevronDown} className="text-textPrimary" />
                  }
                />
              </div>
              <div className="flex py-2 justify-between flex-col items-start gap-2">
                <div
                  className={`w-[21rem] flex items-center md:w-full font-primaryBold ${
                    form.errors.securityAnswer ? 'mb-[30px]' : ''
                  }`}
                ></div>
                <div className="relative">
                  <TextInput
                    label={props.fields?.SpecifyAnswerText?.value}
                    {...form.getInputProps('securityAnswer')}
                    disabled={!form.values.securityQuestion}
                    type={showSecurityAnswer ? 'text' : 'password'}
                    styles={{
                      input: { textAlign: 'left', width: '21rem' }, // Set width to match other input fields
                      error: { maxWidth: '21rem', wordWrap: 'break-word', paddingLeft: '0' },
                    }}
                  />
                  <FontAwesomeIcon
                    icon={showSecurityAnswer ? faEyeSlash : faEye}
                    className="absolute right-[16px] top-[50px] cursor-pointer text-textPrimary"
                    onClick={() => setShowSecurityAnswer(!showSecurityAnswer)}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="w-full px-4 sm:px-0 sm:max-w-[500px] m-0">
            <div className="flex flex-col justify-center my-6 ">
              {props.fields?.YesCheckBoxText?.value && (
                <div className="flex flex-row py-2">
                  <Checkbox
                    classNames={{ input: 'customChecked' }}
                    checked={isYesCheckedForSpecialOffer}
                    onChange={(event) => {
                      setIsYesCheckedForSpecialOffer(event.currentTarget.checked);
                    }}
                    styles={{
                      body: {
                        display: 'flex',
                        alignItems: 'center',
                      },
                    }}
                    radius="xs"
                    label={
                      <div className="flex flex-wrap items-center text-minus1 text-textQuattuordenary text-left">
                        <>
                          <RichText field={{ value: props.fields?.YesCheckBoxText?.value }} />
                        </>
                      </div>
                    }
                  />
                </div>
              )}
              <div className="flex flex-row py-2">
                <Checkbox
                  classNames={{ input: 'customChecked' }}
                  checked={isPaperlessChecked}
                  onChange={(event) => {
                    setIsPaperlessChecked(event.currentTarget.checked);
                  }}
                  styles={{
                    body: {
                      display: 'flex',
                      alignItems: 'center',
                    },
                  }}
                  radius="xs"
                  label={
                    <div className="flex flex-wrap items-center text-minus1 text-textQuattuordenary text-left">
                      <>
                        <RichText field={{ value: props.fields?.IDontCheckBoxText?.value }} />
                        {showToolTip ? (
                          <div className="relative left-[8px] coa-tooltip">
                            <Tooltip
                              content={props.fields?.BillingConsentToolTipText}
                              className="billing-tooltip selected-tooltip"
                              arrowclassName="billing-tooltip-icon"
                            >
                              <QuestionCircle />
                            </Tooltip>
                          </div>
                        ) : (
                          ''
                        )}
                      </>
                    </div>
                  }
                />
              </div>
              {props.fields?.WhyPaperlessBillingTitleText &&
                props.fields?.WhyPaperlessBillingTitleText?.value !== '' && (
                  <button
                    type="button"
                    onClick={() => setShowDetails((val) => !val)}
                    className="flex items-center font-primaryRegular text-textUndenary mt-3 text-minus1 hover:text-textSecondary -tracking-[0.25px] cursor-pointer justify-start"
                  >
                    {props.fields?.WhyPaperlessBillingTitleText?.value}
                    <FontAwesomeIcon
                      icon={showDetails ? faChevronUp : faChevronDown}
                      className="font-primaryRegular text-minus2 pl-2"
                      size="xs"
                    ></FontAwesomeIcon>
                  </button>
                )}
              {showDetails && (
                <>
                  <RichText
                    tag="p"
                    field={props?.fields?.BenefitsofPaperlessText}
                    className="pt-2 flex font-primaryRegular text-textUndenary text-minus1"
                  />
                  <RichText
                    tag="p"
                    field={props?.fields?.PaperlessBillingDropDownText}
                    className="flex font-primaryRegular text-textUndenary mb-5 text-minus1"
                  />
                </>
              )}
              <div className="flex flex-row py-2">
                <Checkbox
                  checked={isAcceptedChecked}
                  onChange={(event) => {
                    const checked = event.currentTarget.checked;
                    setIsAcceptedChecked(checked);

                    if (checked) {
                      setErrorMessage('');
                    }
                  }}
                  classNames={{ input: 'customChecked' }}
                  styles={{
                    body: {
                      display: 'flex',
                      alignItems: 'center',
                    },
                  }}
                  radius="xs"
                  label={
                    <div className="flex flex-wrap items-center text-minus1 text-textQuattuordenary text-left">
                      <RichText
                        tag="p"
                        field={props?.fields?.TermsConditions}
                        className="pt-2 font-primaryRegular sm:flex text-textUndenary text-minus1 page-link"
                      />
                    </div>
                  }
                />
              </div>
            </div>
            {errorMessage && (
              <div
                className="bg-red-100 border border-red-400 text-textDenary px-4 py-3 rounded relative"
                role="alert"
              >
                <span className="block sm:inline">{errorMessage}</span>
              </div>
            )}
            <div className="flex flex-col sm:flex-col py-2 justify-start sm:items-start items-center gap-6 mt-8">
              <div>
                <Button
                  className="sm:w-[240px] px-6 h-[56px] m-0 w-[240px]"
                  icon={
                    loading ? (
                      <FontAwesomeIcon
                        icon={faSpinner}
                        className="text-textQuinary"
                        size="xs"
                        spin
                      />
                    ) : (
                      <>
                        <FontAwesomeIcon icon={faArrowRight} className="hidden" />
                        <FontAwesomeIcon icon={faChevronsRight} className="block" />
                      </>
                    )
                  }
                  disabled={loading}
                  onClick={handleSubmit}
                >
                  {props.fields?.SubmitButtonText?.value}
                </Button>
              </div>
              <div className="flex ">
                <Button
                  className="w-full sm:w-[160px] px-6 m-0 border-none "
                  variant="secondary"
                  disabled={loading}
                  icon={<FontAwesomeIcon icon={faCircleMinus} />}
                  onClick={handleCancel} // Call the handleCancel function when the Cancel button is clicked
                >
                  {props.fields?.CancelButtonText?.value}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export { CombinedComponent };
const Component = withDatasourceCheck()<CombinedProps>(CombinedComponent);
export default aiLogger(Component, Component.name);
